#!/usr/bin/env python3
"""
Advanced Crypto Trading Bot with ML Predictions
Integrates with trained crypto-specific models from a1.py
Supports both test and live trading modes with comprehensive risk management
"""

import os
import json
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# ML and Data Processing
import tensorflow as tf
import joblib
from sklearn.preprocessing import StandardScaler

# Binance API
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException

# Configuration
import configparser
from dataclasses import dataclass, asdict
from threading import Thread, Lock
import asyncio
import signal
import sys

# ========================================================================================
# CONFIGURATION AND DATA CLASSES
# ========================================================================================

@dataclass
class TradingConfig:
    """Trading bot configuration"""
    # API Configuration
    api_key: str = "GWf3NmhOQi9q2KIhDLcugL1smupKLfwSzlSmY3TwexJqNf4ehDpXs4qy2eeZYviW"
    api_secret: str = "gJCCFfWoUCzDFfs9b6JCH6Q6yMg3vKSqxK5k5EnQ4rwHJXT6jPBvnyVaaJky14pC"
    testnet: bool = True
    
    # Trading Parameters
    max_budget: float = 30.0
    trade_amount: float = 14.0
    max_concurrent_trades: int = 2
    min_budget_for_multiple_trades: float = 28.0
    
    # ML Prediction Parameters
    prediction_threshold: float = 1.9  # Minimum predicted gain % to trigger buy
    take_profit: float = 1.2  # Take profit %
    stop_loss: float = 1.5   # Stop loss %
    max_loss: float = 1.6    # Maximum loss % (emergency exit)
    
    # Technical Parameters
    trading_fee: float = 0.1  # Trading fee %
    prediction_horizon: int = 10  # Number of future steps to predict
    sequence_length: int = 60    # Input sequence length for models
    
    # Monitoring Parameters
    update_interval: int = 300   # Data update interval in seconds (5 minutes)
    log_level: str = "DEBUG"
    
    # Model Paths
    models_path: str = "models"
    scalers_path: str = "scalers"
    
    # Crypto Pairs to Monitor
    crypto_pairs: List[str] = None
    
    def __post_init__(self):
        if self.crypto_pairs is None:
            self.crypto_pairs = ["XRPUSDT"] 

@dataclass
class TradePosition:
    """Represents an active trading position"""
    symbol: str
    side: str  # 'BUY' or 'SELL'
    quantity: float
    entry_price: float
    entry_time: datetime
    order_id: str
    oco_order_id: Optional[str] = None
    predicted_gain: float = 0.0
    current_pnl: float = 0.0
    status: str = "ACTIVE"  # ACTIVE, CLOSED, STOPPED

@dataclass
class PredictionResult:
    """ML model prediction result"""
    symbol: str
    current_price: float
    predicted_prices: List[float]
    predicted_gain: float  # Final step gain
    max_gain: float  # Best gain within prediction horizon
    max_gain_step: int  # Step where max gain occurs
    min_gain: float  # Worst loss within prediction horizon
    target_reached: bool  # Whether target gain is reached at any point
    confidence: float
    timestamp: datetime

# ========================================================================================
# MAXIMUM TECHNICAL ANALYZER (Same as training in a1.py)
# ========================================================================================

class MaximumTechnicalAnalyzer:
    def __init__(self, sequence_length=60):
        self.sequence_length = sequence_length
        self.scaler = StandardScaler()

    def create_multi_horizon_target(self, df, horizon=10, target_type="regression"):
        """
        Create multi-step ahead targets

        Args:
            df: DataFrame with OHLCV data
            horizon: Number of steps to predict ahead
            target_type: "regression" for price prediction, "classification" for signals
        """
        df = df.copy()

        if target_type == "regression":
            # Predict future close prices (normalized returns)
            targets = []
            for i in range(len(df)):
                if i >= len(df) - horizon:
                    # Pad with zeros for last samples
                    targets.append([0.0] * horizon)
                    continue

                current_price = df['Close'].iloc[i]
                future_prices = []

                for step in range(1, horizon + 1):
                    if i + step < len(df):
                        future_price = df['Close'].iloc[i + step]
                        # Normalized return
                        normalized_return = (future_price - current_price) / current_price
                        future_prices.append(normalized_return)
                    else:
                        future_prices.append(0.0)

                targets.append(future_prices)

            # Create target columns
            for step in range(horizon):
                df[f'Target_Step_{step+1}'] = [target[step] for target in targets]

        else:  # classification
            # Predict future signal directions
            profit_threshold = 0.015
            loss_threshold = -0.015

            targets = []
            for i in range(len(df)):
                if i >= len(df) - horizon:
                    targets.append([0] * horizon)
                    continue

                current_price = df['Close'].iloc[i]
                future_signals = []

                for step in range(1, horizon + 1):
                    if i + step < len(df):
                        future_price = df['Close'].iloc[i + step]
                        return_pct = (future_price - current_price) / current_price

                        if return_pct >= profit_threshold:
                            signal = 1  # Buy signal
                        elif return_pct <= loss_threshold:
                            signal = 0  # Sell signal
                        else:
                            signal = 0.5  # Hold signal

                        future_signals.append(signal)
                    else:
                        future_signals.append(0)

                targets.append(future_signals)

            # Create target columns
            for step in range(horizon):
                df[f'Target_Step_{step+1}'] = [target[step] for target in targets]

        return df
    # Basic calculation functions
    def sma(self, data, period): return data.rolling(window=period).mean()
    def ema(self, data, period): return data.ewm(span=period).mean()
    def wma(self, data, period):
        weights = np.arange(1, period + 1)
        return data.rolling(period).apply(lambda x: np.dot(x, weights) / weights.sum(), raw=True)
    def tema(self, data, period):
        ema1 = self.ema(data, period)
        ema2 = self.ema(ema1, period)
        ema3 = self.ema(ema2, period)
        return 3 * (ema1 - ema2) + ema3
    def dema(self, data, period):
        ema1 = self.ema(data, period)
        ema2 = self.ema(ema1, period)
        return 2 * ema1 - ema2
    def hma(self, data, period):
        # Hull Moving Average[8]
        wma1 = self.wma(data, period // 2) * 2
        wma2 = self.wma(data, period)
        raw_hma = wma1 - wma2
        return self.wma(raw_hma, int(np.sqrt(period)))
    def kama(self, data, period=10):
        change = abs(data - data.shift(period))
        volatility = abs(data - data.shift(1)).rolling(period).sum()
        er = change / volatility
        sc = ((er * (2.0 / (2 + 1) - 2.0 / (30 + 1)) + 2.0 / (30 + 1)) ** 2.0)
        kama = [data.iloc[0]]
        for i in range(1, len(data)):
            kama.append(kama[-1] + sc.iloc[i] * (data.iloc[i] - kama[-1]))
        return pd.Series(kama, index=data.index)

    def rsi(self, data, period=14):
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def stochastic(self, high, low, close, k_period=14, d_period=3):
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)
        d_percent = k_percent.rolling(window=d_period).mean()
        return k_percent, d_percent

    def williams_r(self, high, low, close, period=14):
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        return -100 * (highest_high - close) / (highest_high - lowest_low)

    def cci(self, high, low, close, period=20):
        tp = (high + low + close) / 3
        ma = tp.rolling(period).mean()
        md = tp.rolling(period).apply(lambda x: np.mean(np.abs(x - x.mean())), raw=True)
        return (tp - ma) / (0.015 * md)

    def ultimate_oscillator(self, high, low, close, p1=7, p2=14, p3=28):
        tr = self.true_range(high, low, close)
        bp = close - np.minimum(low, close.shift(1))
        avg7 = bp.rolling(p1).sum() / tr.rolling(p1).sum()
        avg14 = bp.rolling(p2).sum() / tr.rolling(p2).sum()
        avg28 = bp.rolling(p3).sum() / tr.rolling(p3).sum()
        return 100 * (4 * avg7 + 2 * avg14 + avg28) / 7

    def macd(self, data, fast=12, slow=26, signal=9):
        ema_fast = self.ema(data, fast)
        ema_slow = self.ema(data, slow)
        macd = ema_fast - ema_slow
        signal_line = self.ema(macd, signal)
        histogram = macd - signal_line
        return macd, signal_line, histogram

    def bollinger_bands(self, data, period=20, std_dev=2):
        sma = self.sma(data, period)
        std = data.rolling(window=period).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower

    def keltner_channels(self, high, low, close, period=20, multiplier=2):
        # Keltner Channels[12]
        ema = self.ema(close, period)
        atr = self.atr(high, low, close, period)
        upper = ema + (multiplier * atr)
        lower = ema - (multiplier * atr)
        return upper, ema, lower

    def true_range(self, high, low, close):
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        return pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

    def atr(self, high, low, close, period=14):
        return self.true_range(high, low, close).rolling(window=period).mean()

    def adx(self, high, low, close, period=14):
        tr = self.true_range(high, low, close)
        plus_dm = high.diff()
        minus_dm = -low.diff()
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0
        plus_di = 100 * (plus_dm.rolling(period).mean() / tr.rolling(period).mean())
        minus_di = 100 * (minus_dm.rolling(period).mean() / tr.rolling(period).mean())
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(period).mean()
        return adx, plus_di, minus_di

    def aroon(self, high, low, period=25):
        aroon_up = high.rolling(period + 1).apply(lambda x: x.argmax(), raw=True) / period * 100
        aroon_down = low.rolling(period + 1).apply(lambda x: x.argmin(), raw=True) / period * 100
        return aroon_up, aroon_down

    def obv(self, close, volume):
        obv = [0]
        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.append(obv[-1] + volume.iloc[i])
            elif close.iloc[i] < close.iloc[i-1]:
                obv.append(obv[-1] - volume.iloc[i])
            else:
                obv.append(obv[-1])
        return pd.Series(obv, index=close.index)

    def vwap(self, high, low, close, volume):
        typical_price = (high + low + close) / 3
        return (typical_price * volume).cumsum() / volume.cumsum()

    def mfi(self, high, low, close, volume, period=14):
        # Money Flow Index[12]
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0).rolling(period).sum()
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0).rolling(period).sum()
        return 100 - (100 / (1 + positive_flow / negative_flow))

    def chaikin_money_flow(self, high, low, close, volume, period=20):
        # Chaikin Money Flow[12]
        mfv = ((close - low) - (high - close)) / (high - low) * volume
        return mfv.rolling(period).sum() / volume.rolling(period).sum()

    def chaikin_oscillator(self, high, low, close, volume):
        # Chaikin Oscillator[12]
        ad_line = ((close - low) - (high - close)) / (high - low) * volume
        ad_line = ad_line.fillna(0).cumsum()
        return self.ema(ad_line, 3) - self.ema(ad_line, 10)

    def awesome_oscillator(self, high, low):
        # Bill Williams Awesome Oscillator[12]
        median_price = (high + low) / 2
        ao = self.sma(median_price, 5) - self.sma(median_price, 34)
        return ao

    def accelerator_decelerator(self, high, low, close):
        # Bill Williams Accelerator/Decelerator[12]
        ao = self.awesome_oscillator(high, low)
        ac = ao - self.sma(ao, 5)
        return ac

    def market_facilitation_index(self, high, low, volume):
        # Bill Williams Market Facilitation Index[12]
        return (high - low) / volume

    def wave_trend_oscillator(self, high, low, close, period1=10, period2=21):
        # Wave Trend Oscillator[12]
        ap = (high + low + close) / 3
        esa = self.ema(ap, period1)
        d = self.ema(abs(ap - esa), period1)
        ci = (ap - esa) / (0.015 * d)
        tci = self.ema(ci, period2)
        return tci

    def dinapoli_macd(self, data, fast=8, slow=17, signal=9):
        # DiNapoli MACD[12]
        return self.macd(data, fast, slow, signal)

    def ichimoku_cloud(self, high, low, close):
        # Complete Ichimoku Cloud System[8][9]
        # Tenkan-sen (Conversion Line)
        tenkan = (high.rolling(9).max() + low.rolling(9).min()) / 2

        # Kijun-sen (Base Line)
        kijun = (high.rolling(26).max() + low.rolling(26).min()) / 2

        # Senkou Span A (Leading Span A)
        senkou_a = ((tenkan + kijun) / 2).shift(26)

        # Senkou Span B (Leading Span B)
        senkou_b = ((high.rolling(52).max() + low.rolling(52).min()) / 2).shift(26)

        # Chikou Span (Lagging Span)
        chikou = close.shift(-26)

        return tenkan, kijun, senkou_a, senkou_b, chikou

    def heikin_ashi(self, open_price, high, low, close):
        # Heikin Ashi Candlesticks[8][9]
        ha_close = (open_price + high + low + close) / 4
        ha_open = pd.Series(index=close.index, dtype='float64')
        ha_open.iloc[0] = (open_price.iloc[0] + close.iloc[0]) / 2

        for i in range(1, len(close)):
            ha_open.iloc[i] = (ha_open.iloc[i-1] + ha_close.iloc[i-1]) / 2

        ha_high = pd.concat([high, ha_open, ha_close], axis=1).max(axis=1)
        ha_low = pd.concat([low, ha_open, ha_close], axis=1).min(axis=1)

        return ha_open, ha_high, ha_low, ha_close

    def chande_momentum_oscillator(self, data, period=14):
        momentum = data.diff()
        sum_up = momentum.where(momentum > 0, 0).rolling(period).sum()
        sum_down = momentum.where(momentum < 0, 0).abs().rolling(period).sum()
        return 100 * (sum_up - sum_down) / (sum_up + sum_down)

    def volume_rate_of_change(self, volume, period=14):
        # Volume Rate of Change[12]
        return (volume / volume.shift(period) - 1) * 100

    def volume_oscillator(self, volume, short_period=5, long_period=10):
        short_ma = self.sma(volume, short_period)
        long_ma = self.sma(volume, long_period)
        return (short_ma - long_ma) / long_ma * 100

    def safe_divide(self, a, b):
        return np.where(b != 0, a / b, 0)

    def optimize_dtypes(self, df):
        """Optimize data types to reduce memory usage"""
        for col, dt in df.dtypes.items():
            if dt.kind in "iu":
                df[col] = pd.to_numeric(df[col], downcast="unsigned" if (df[col] >= 0).all() else "integer")
            elif dt.kind == "f":
                df[col] = pd.to_numeric(df[col], downcast="float")
            elif dt == "object":
                nunique = df[col].nunique(dropna=False)
                if nunique / len(df) < 0.5:
                    df[col] = df[col].astype("category")
        return df

    def detect_harmonic_patterns(self, high, low, close):
        # Harmonic Pattern Detection[10]
        patterns = {}

        # Gartley Pattern ratios
        patterns['GARTLEY_XA'] = (close / close.shift(5) - 1) * 100  # XA leg
        patterns['GARTLEY_AB'] = (close / close.shift(3) - 1) * 100  # AB leg
        patterns['GARTLEY_BC'] = (close / close.shift(2) - 1) * 100  # BC leg

        # Butterfly Pattern ratios
        patterns['BUTTERFLY_XA'] = (close / close.shift(7) - 1) * 100
        patterns['BUTTERFLY_AB'] = (close / close.shift(4) - 1) * 100

        # Bat Pattern ratios
        patterns['BAT_XA'] = (close / close.shift(6) - 1) * 100
        patterns['BAT_AB'] = (close / close.shift(3) - 1) * 100

        # Crab Pattern ratios
        patterns['CRAB_XA'] = (close / close.shift(8) - 1) * 100
        patterns['CRAB_AB'] = (close / close.shift(5) - 1) * 100

        return patterns

    def elliott_wave_ratios(self, close):
        # Elliott Wave Theory ratios[10]
        ratios = {}

        # Wave relationships
        ratios['WAVE_RATIO_618'] = (close / close.shift(5) - 1) / 0.618
        ratios['WAVE_RATIO_382'] = (close / close.shift(3) - 1) / 0.382
        ratios['WAVE_RATIO_236'] = (close / close.shift(2) - 1) / 0.236
        ratios['WAVE_RATIO_1618'] = (close / close.shift(8) - 1) / 1.618

        # Impulse wave characteristics
        ratios['IMPULSE_WAVE_1'] = close - close.shift(5)
        ratios['IMPULSE_WAVE_3'] = close - close.shift(3)
        ratios['IMPULSE_WAVE_5'] = close - close.shift(1)

        # Corrective wave characteristics
        ratios['CORRECTIVE_A'] = close - close.shift(4)
        ratios['CORRECTIVE_B'] = close - close.shift(2)
        ratios['CORRECTIVE_C'] = close - close.shift(1)

        return ratios

    def advanced_candlestick_patterns(self, open_price, high, low, close):
        # Enhanced Candlestick Pattern Recognition[8]
        patterns = {}

        # Basic patterns (already exist, expanding)
        body = abs(close - open_price)
        range_hl = high - low
        upper_shadow = high - np.maximum(open_price, close)
        lower_shadow = np.minimum(open_price, close) - low

        # Advanced patterns
        patterns['ABANDONED_BABY'] = (
            (abs(high.shift(1) - low) > 0) &
            (abs(high.shift(1) - low.shift(-1)) > 0) &
            (body.shift(1) < range_hl.shift(1) * 0.1)
        ).astype(int)

        patterns['BELT_HOLD_BULL'] = (
            (close > open_price) &
            (lower_shadow < body * 0.1) &
            (body > range_hl * 0.7)
        ).astype(int)

        patterns['BELT_HOLD_BEAR'] = (
            (close < open_price) &
            (upper_shadow < body * 0.1) &
            (body > range_hl * 0.7)
        ).astype(int)

        patterns['BREAKAWAY'] = (
            (close.shift(4) < open_price.shift(4)) &  # First bearish
            (low.shift(3) > high.shift(4)) &  # Gap down
            (close.shift(3) < open_price.shift(3)) &  # Second bearish
            (close.shift(2) < open_price.shift(2)) &  # Third bearish
            (close.shift(1) < open_price.shift(1)) &  # Fourth bearish
            (close > open_price) &  # Fifth bullish
            (close > open_price.shift(4))  # Close above first candle
        ).astype(int)

        patterns['CONCEALING_BABY_SWALLOW'] = (
            (close.shift(3) < open_price.shift(3)) &  # First bearish
            (close.shift(2) < open_price.shift(2)) &  # Second bearish
            (close.shift(1) < open_price.shift(1)) &  # Third bearish
            (close < open_price) &  # Fourth bearish
            (close.shift(1) > close.shift(2)) &  # Third closes higher
            (close > close.shift(1))  # Fourth closes higher
        ).astype(int)

        patterns['COUNTERATTACK'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close > open_price) &  # Second bullish
            (abs(close - close.shift(1)) < range_hl.shift(1) * 0.1)  # Same close
        ).astype(int)

        patterns['DRAGONFLY_DOJI'] = (
            (abs(close - open_price) < range_hl * 0.1) &
            (upper_shadow < range_hl * 0.1) &
            (lower_shadow > range_hl * 0.6)
        ).astype(int)

        patterns['GRAVESTONE_DOJI'] = (
            (abs(close - open_price) < range_hl * 0.1) &
            (lower_shadow < range_hl * 0.1) &
            (upper_shadow > range_hl * 0.6)
        ).astype(int)

        patterns['HANGING_MAN'] = (
            (lower_shadow > body * 2) &
            (upper_shadow < body * 0.3) &
            (close < open_price) &
            (close.shift(1) > open_price.shift(1))  # Previous bullish trend
        ).astype(int)

        patterns['INVERTED_HAMMER'] = (
            (upper_shadow > body * 2) &
            (lower_shadow < body * 0.3) &
            (close.shift(1) < open_price.shift(1))  # Previous bearish trend
        ).astype(int)

        patterns['LONG_LEGGED_DOJI'] = (
            (abs(close - open_price) < range_hl * 0.1) &
            (upper_shadow > range_hl * 0.3) &
            (lower_shadow > range_hl * 0.3)
        ).astype(int)

        patterns['MARUBOZU_WHITE'] = (
            (close > open_price) &
            (upper_shadow < range_hl * 0.01) &
            (lower_shadow < range_hl * 0.01)
        ).astype(int)

        patterns['MARUBOZU_BLACK'] = (
            (close < open_price) &
            (upper_shadow < range_hl * 0.01) &
            (lower_shadow < range_hl * 0.01)
        ).astype(int)

        patterns['MATCHING_LOW'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close < open_price) &  # Second bearish
            (abs(low - low.shift(1)) < range_hl * 0.05)  # Same lows
        ).astype(int)

        patterns['ON_NECK'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close > open_price) &  # Second bullish
            (close <= close.shift(1) + range_hl.shift(1) * 0.05)  # Closes at previous close
        ).astype(int)

        patterns['PIERCING_LINE'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close > open_price) &  # Second bullish
            (open_price < low.shift(1)) &  # Opens below previous low
            (close > (open_price.shift(1) + close.shift(1)) / 2)  # Closes above midpoint
        ).astype(int)

        patterns['RICKSHAW_MAN'] = (
            (abs(close - open_price) < range_hl * 0.1) &
            (upper_shadow > range_hl * 0.25) &
            (lower_shadow > range_hl * 0.25)
        ).astype(int)

        patterns['RISING_THREE_METHODS'] = (
            (close.shift(4) > open_price.shift(4)) &  # First bullish
            (close.shift(3) < open_price.shift(3)) &  # Second bearish
            (close.shift(2) < open_price.shift(2)) &  # Third bearish
            (close.shift(1) < open_price.shift(1)) &  # Fourth bearish
            (close > open_price) &  # Fifth bullish
            (close > close.shift(4)) &  # Closes above first
            (high.shift(3) < close.shift(4)) &  # Contained within first
            (high.shift(2) < close.shift(4)) &
            (high.shift(1) < close.shift(4))
        ).astype(int)

        patterns['SEPARATING_LINES'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close > open_price) &  # Second bullish
            (abs(open_price - open_price.shift(1)) < range_hl * 0.05)  # Same opens
        ).astype(int)

        patterns['SHOOTING_STAR'] = (
            (upper_shadow > body * 2) &
            (lower_shadow < body * 0.3) &
            (close < open_price) &
            (close.shift(1) > open_price.shift(1))  # Previous bullish trend
        ).astype(int)

        patterns['SPINNING_TOP'] = (
            (body < range_hl * 0.3) &
            (upper_shadow > body) &
            (lower_shadow > body)
        ).astype(int)

        patterns['STALLED_PATTERN'] = (
            (close.shift(2) > open_price.shift(2)) &  # First bullish
            (close.shift(1) > open_price.shift(1)) &  # Second bullish
            (close > open_price) &  # Third bullish
            (body.shift(1) > body.shift(2)) &  # Second larger than first
            (body < body.shift(1)) &  # Third smaller than second
            (upper_shadow > body * 0.5)  # Third has upper shadow
        ).astype(int)

        patterns['STICK_SANDWICH'] = (
            (close.shift(2) < open_price.shift(2)) &  # First bearish
            (close.shift(1) > open_price.shift(1)) &  # Second bullish
            (close < open_price) &  # Third bearish
            (abs(close - close.shift(2)) < range_hl * 0.05)  # Same closes
        ).astype(int)

        patterns['TAKURI'] = (
            (lower_shadow > body * 3) &
            (upper_shadow < body * 0.1) &
            (body < range_hl * 0.2)
        ).astype(int)

        patterns['TASUKI_GAP'] = (
            (close.shift(2) > open_price.shift(2)) &  # First bullish
            (close.shift(1) > open_price.shift(1)) &  # Second bullish
            (open_price.shift(1) > close.shift(2)) &  # Gap up
            (close < open_price) &  # Third bearish
            (close < close.shift(1)) &  # Closes below second
            (close > close.shift(2))  # But above first
        ).astype(int)

        patterns['THREE_INSIDE_UP'] = (
            (close.shift(2) < open_price.shift(2)) &  # First bearish
            (close.shift(1) > open_price.shift(1)) &  # Second bullish (inside first)
            (open_price.shift(1) > close.shift(2)) &  # Opens above first close
            (close.shift(1) < open_price.shift(2)) &  # Closes below first open
            (close > open_price) &  # Third bullish
            (close > close.shift(1))  # Closes above second
        ).astype(int)

        patterns['THREE_OUTSIDE_UP'] = (
            (close.shift(2) < open_price.shift(2)) &  # First bearish
            (close.shift(1) > open_price.shift(1)) &  # Second bullish (engulfs first)
            (open_price.shift(1) < close.shift(2)) &  # Opens below first close
            (close.shift(1) > open_price.shift(2)) &  # Closes above first open
            (close > open_price) &  # Third bullish
            (close > close.shift(1))  # Closes above second
        ).astype(int)

        patterns['THRUSTING'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close > open_price) &  # Second bullish
            (open_price < close.shift(1)) &  # Opens below first close
            (close < (open_price.shift(1) + close.shift(1)) / 2)  # Closes below midpoint
        ).astype(int)

        patterns['TRISTAR'] = (
            (abs(close.shift(2) - open_price.shift(2)) < range_hl.shift(2) * 0.1) &  # First doji
            (abs(close.shift(1) - open_price.shift(1)) < range_hl.shift(1) * 0.1) &  # Second doji
            (abs(close - open_price) < range_hl * 0.1) &  # Third doji
            (high.shift(1) > high.shift(2)) &  # Second higher than first
            (high.shift(1) > high) &  # Second higher than third
            (low.shift(1) < low.shift(2)) &  # Second lower than first
            (low.shift(1) < low)  # Second lower than third
        ).astype(int)

        patterns['UNIQUE_THREE_RIVER_BOTTOM'] = (
            (close.shift(2) < open_price.shift(2)) &  # First bearish
            (close.shift(1) < open_price.shift(1)) &  # Second bearish
            (abs(close - open_price) < range_hl * 0.1) &  # Third doji
            (low.shift(1) < low.shift(2)) &  # Second makes new low
            (low > low.shift(1))  # Third above second low
        ).astype(int)

        patterns['UPSIDE_GAP_TWO_CROWS'] = (
            (close.shift(2) > open_price.shift(2)) &  # First bullish
            (close.shift(1) < open_price.shift(1)) &  # Second bearish
            (close < open_price) &  # Third bearish
            (open_price.shift(1) > close.shift(2)) &  # Gap up
            (close < close.shift(1)) &  # Third closes below second
            (open_price > open_price.shift(1))  # Third opens above second
        ).astype(int)

        return patterns

    # COMPREHENSIVE INDICATOR COLLECTION (300+ FEATURES)
    def add_maximum_technical_indicators(self, df):
        """Add 300+ comprehensive technical indicators and patterns"""
        df = df.copy()
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        if not all(col in df.columns for col in required_cols):
            return None

        for col in required_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        high, low, close, open_price, volume = df['High'], df['Low'], df['Close'], df['Open'], df['Volume']

        print("🔄 Adding MAXIMUM technical indicators (300+)...")

        # 1. MOVING AVERAGES (60 features) - Enhanced from previous
        ma_periods = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 25, 26, 30, 34, 40, 50, 55, 60, 89, 100, 120, 144, 200]
        for period in ma_periods:
            if period <= len(df):
                df[f'SMA_{period}'] = self.sma(close, period)
                df[f'EMA_{period}'] = self.ema(close, period)

        # Advanced moving averages
        adv_ma_periods = [8, 10, 13, 20, 21, 34, 55, 89]
        for period in adv_ma_periods:
            if period <= len(df):
                df[f'WMA_{period}'] = self.wma(close, period)
                df[f'TEMA_{period}'] = self.tema(close, period)
                df[f'DEMA_{period}'] = self.dema(close, period)
                df[f'HMA_{period}'] = self.hma(close, period)

        # Kaufman Adaptive MA
        for period in [10, 14, 20, 30]:
            df[f'KAMA_{period}'] = self.kama(close, period)

        # 2. MOMENTUM OSCILLATORS (80 features) - Massively enhanced
        rsi_periods = [2, 6, 7, 9, 11, 14, 17, 21, 25, 30]
        for period in rsi_periods:
            df[f'RSI_{period}'] = self.rsi(close, period)
            df[f'RSI_VOLUME_{period}'] = self.rsi(volume, period)
            df[f'RSI_HIGH_{period}'] = self.rsi(high, period)
            df[f'RSI_LOW_{period}'] = self.rsi(low, period)

        # Stochastic variants
        stoch_configs = [(5,3), (8,3), (14,3), (21,5), (25,5), (34,8)]
        for k, d in stoch_configs:
            k_val, d_val = self.stochastic(high, low, close, k, d)
            df[f'STOCH_K_{k}_{d}'] = k_val
            df[f'STOCH_D_{k}_{d}'] = d_val
            df[f'STOCH_RSI_{k}'] = self.stochastic(df[f'RSI_{k}'], df[f'RSI_{k}'], df[f'RSI_{k}'], 14)[0] if f'RSI_{k}' in df.columns else 0

        # Williams %R variants
        willr_periods = [6, 10, 14, 21, 28, 34]
        for period in willr_periods:
            df[f'WILLR_{period}'] = self.williams_r(high, low, close, period)

        # CCI variants
        cci_periods = [14, 17, 20, 25, 30, 34, 40]
        for period in cci_periods:
            df[f'CCI_{period}'] = self.cci(high, low, close, period)

        # Advanced oscillators
        df['UO'] = self.ultimate_oscillator(high, low, close)
        df['CMO_14'] = self.chande_momentum_oscillator(close, 14)
        df['CMO_20'] = self.chande_momentum_oscillator(close, 20)

        # Bill Williams Indicators[12]
        df['AWESOME_OSC'] = self.awesome_oscillator(high, low)
        df['AC_OSC'] = self.accelerator_decelerator(high, low, close)
        df['MFI_BW'] = self.market_facilitation_index(high, low, volume)

        # Wave Trend Oscillator[12]
        df['WTO_10_21'] = self.wave_trend_oscillator(high, low, close, 10, 21)
        df['WTO_14_28'] = self.wave_trend_oscillator(high, low, close, 14, 28)

        # 3. TREND INDICATORS (35 features) - Enhanced
        adx_periods = [14, 17, 20, 25, 28]
        for period in adx_periods:
            adx_val, plus_di, minus_di = self.adx(high, low, close, period)
            df[f'ADX_{period}'] = adx_val
            df[f'PLUS_DI_{period}'] = plus_di
            df[f'MINUS_DI_{period}'] = minus_di
            df[f'DX_{period}'] = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)

        # Aroon variants
        aroon_periods = [14, 20, 25, 28, 30]
        for period in aroon_periods:
            aroon_up, aroon_down = self.aroon(high, low, period)
            df[f'AROON_UP_{period}'] = aroon_up
            df[f'AROON_DOWN_{period}'] = aroon_down
            df[f'AROON_OSC_{period}'] = aroon_up - aroon_down

        # Ichimoku Cloud System[8][9]
        tenkan, kijun, senkou_a, senkou_b, chikou = self.ichimoku_cloud(high, low, close)
        df['ICHIMOKU_TENKAN'] = tenkan
        df['ICHIMOKU_KIJUN'] = kijun
        df['ICHIMOKU_SENKOU_A'] = senkou_a
        df['ICHIMOKU_SENKOU_B'] = senkou_b
        df['ICHIMOKU_CHIKOU'] = chikou

        # Cloud analysis
        df['ICHIMOKU_CLOUD_GREEN'] = (senkou_a > senkou_b).astype(int)
        df['ICHIMOKU_ABOVE_CLOUD'] = (close > np.maximum(senkou_a, senkou_b)).astype(int)
        df['ICHIMOKU_BELOW_CLOUD'] = (close < np.minimum(senkou_a, senkou_b)).astype(int)

        # 4. MACD VARIANTS (30 features) - Enhanced
        macd_configs = [(5,13,5), (8,21,5), (12,26,9), (19,39,9), (6,12,6), (24,52,18), (7,14,7), (15,30,10)]
        for fast, slow, signal in macd_configs:
            macd_val, signal_val, hist_val = self.macd(close, fast, slow, signal)
            df[f'MACD_{fast}_{slow}'] = macd_val
            df[f'MACD_SIGNAL_{fast}_{slow}'] = signal_val
            df[f'MACD_HIST_{fast}_{slow}'] = hist_val

        # DiNapoli MACD[12]
        dinapoli_macd, dinapoli_signal, dinapoli_hist = self.dinapoli_macd(close)
        df['DINAPOLI_MACD'] = dinapoli_macd
        df['DINAPOLI_SIGNAL'] = dinapoli_signal
        df['DINAPOLI_HIST'] = dinapoli_hist

        # 5. BOLLINGER BANDS & VARIANTS (40 features) - Enhanced
        bb_configs = [(10,1.5), (10,2), (15,1.8), (20,1.5), (20,2), (20,2.5), (25,2), (30,2), (34,2), (50,2)]
        for period, std_dev in bb_configs:
            bb_upper, bb_middle, bb_lower = self.bollinger_bands(close, period, std_dev)
            df[f'BB_UPPER_{period}_{int(std_dev*10)}'] = bb_upper
            df[f'BB_LOWER_{period}_{int(std_dev*10)}'] = bb_lower
            df[f'BB_WIDTH_{period}_{int(std_dev*10)}'] = (bb_upper - bb_lower) / bb_middle
            df[f'BB_POSITION_{period}_{int(std_dev*10)}'] = (close - bb_lower) / (bb_upper - bb_lower)

        # Keltner Channels[12]
        keltner_periods = [10, 14, 20, 28]
        for period in keltner_periods:
            kc_upper, kc_middle, kc_lower = self.keltner_channels(high, low, close, period)
            df[f'KC_UPPER_{period}'] = kc_upper
            df[f'KC_MIDDLE_{period}'] = kc_middle
            df[f'KC_LOWER_{period}'] = kc_lower
            df[f'KC_WIDTH_{period}'] = (kc_upper - kc_lower) / kc_middle

        # 6. VOLATILITY INDICATORS (25 features) - Enhanced
        atr_periods = [7, 10, 14, 17, 20, 21, 28, 30, 34, 50]
        for period in atr_periods:
            df[f'ATR_{period}'] = self.atr(high, low, close, period)
            df[f'ATR_PERCENT_{period}'] = df[f'ATR_{period}'] / close * 100

        # Volatility measures
        for period in [5, 10, 14, 20, 30, 50]:
            df[f'VOLATILITY_{period}'] = close.rolling(period).std() / close.rolling(period).mean()
            df[f'PRICE_RANGE_{period}'] = (high.rolling(period).max() - low.rolling(period).min()) / close

        # 7. VOLUME INDICATORS (35 features) - Massively enhanced
        df['OBV'] = self.obv(close, volume)
        df['VWAP'] = self.vwap(high, low, close, volume)

        # Money Flow Index variants[12]
        mfi_periods = [10, 14, 17, 20, 25, 28]
        for period in mfi_periods:
            df[f'MFI_{period}'] = self.mfi(high, low, close, volume, period)

        # Chaikin indicators[12]
        df['CHAIKIN_OSC'] = self.chaikin_oscillator(high, low, close, volume)
        chaikin_periods = [10, 14, 20, 28]
        for period in chaikin_periods:
            df[f'CHAIKIN_MF_{period}'] = self.chaikin_money_flow(high, low, close, volume, period)

        # Volume analysis
        for period in [5, 10, 14, 15, 20, 25, 30, 50]:
            df[f'VOLUME_SMA_{period}'] = self.sma(volume, period)
            df[f'VOLUME_RATIO_{period}'] = volume / df[f'VOLUME_SMA_{period}']
            df[f'VOLUME_ROC_{period}'] = self.volume_rate_of_change(volume, period)

        # Volume oscillators
        vol_osc_configs = [(5,10), (10,20), (14,28)]
        for short, long in vol_osc_configs:
            df[f'VOLUME_OSC_{short}_{long}'] = self.volume_oscillator(volume, short, long)

        df['VOLUME_PRICE_TREND'] = ((close - close.shift(1)) / close.shift(1) * volume).cumsum()
        df['EASE_OF_MOVEMENT'] = ((high + low) / 2 - (high.shift(1) + low.shift(1)) / 2) * volume / (high - low)
        df['ACCUMULATION_DISTRIBUTION'] = ((close - low) - (high - close)) / (high - low) * volume
        df['ACCUMULATION_DISTRIBUTION'] = df['ACCUMULATION_DISTRIBUTION'].fillna(0).cumsum()

        # 8. PRICE ACTION & PATTERNS (40 features) - Enhanced
        df['TYPICAL_PRICE'] = (high + low + close) / 3
        df['MEDIAN_PRICE'] = (high + low) / 2
        df['WEIGHTED_CLOSE'] = (high + low + 2*close) / 4
        df['RANGE'] = high - low
        df['BODY'] = abs(close - open_price)
        df['UPPER_SHADOW'] = high - np.maximum(open_price, close)
        df['LOWER_SHADOW'] = np.minimum(open_price, close) - low
        df['BODY_RANGE_RATIO'] = df['BODY'] / df['RANGE']
        df['UPPER_SHADOW_RATIO'] = df['UPPER_SHADOW'] / df['RANGE']
        df['LOWER_SHADOW_RATIO'] = df['LOWER_SHADOW'] / df['RANGE']

        # Price ratios
        df['HL_RATIO'] = self.safe_divide(high, low)
        df['CO_RATIO'] = self.safe_divide(close, open_price)
        df['HC_RATIO'] = self.safe_divide(high, close)
        df['LC_RATIO'] = self.safe_divide(low, close)
        df['OC_RATIO'] = self.safe_divide(open_price, close)
        df['HO_RATIO'] = self.safe_divide(high, open_price)
        df['LO_RATIO'] = self.safe_divide(low, open_price)

        # Heikin Ashi[8][9]
        ha_open, ha_high, ha_low, ha_close = self.heikin_ashi(open_price, high, low, close)
        df['HA_OPEN'] = ha_open
        df['HA_HIGH'] = ha_high
        df['HA_LOW'] = ha_low
        df['HA_CLOSE'] = ha_close
        df['HA_BODY'] = abs(ha_close - ha_open)
        df['HA_UPPER_SHADOW'] = ha_high - np.maximum(ha_open, ha_close)
        df['HA_LOWER_SHADOW'] = np.minimum(ha_open, ha_close) - ha_low

        # Gap analysis
        df['GAP_UP'] = (low > high.shift(1)).astype(int)
        df['GAP_DOWN'] = (high < low.shift(1)).astype(int)
        df['GAP_SIZE'] = np.where(df['GAP_UP'], low - high.shift(1),
                                 np.where(df['GAP_DOWN'], low.shift(1) - high, 0))
        df['GAP_PERCENTAGE'] = df['GAP_SIZE'] / close.shift(1) * 100

        # Support/Resistance levels
        for window in [5, 8, 10, 13, 15, 20, 21, 25, 30, 34, 50, 55]:
            df[f'RESISTANCE_{window}'] = high.rolling(window).max()
            df[f'SUPPORT_{window}'] = low.rolling(window).min()
            df[f'RESISTANCE_DISTANCE_{window}'] = (df[f'RESISTANCE_{window}'] - close) / close
            df[f'SUPPORT_DISTANCE_{window}'] = (close - df[f'SUPPORT_{window}']) / close
            df[f'SUPPORT_RESISTANCE_RATIO_{window}'] = df[f'SUPPORT_DISTANCE_{window}'] / df[f'RESISTANCE_DISTANCE_{window}']

        # 9. ADVANCED MOMENTUM (30 features) - Enhanced
        momentum_periods = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 18, 20, 21, 25, 28, 30]
        for period in momentum_periods:
            df[f'MOMENTUM_{period}'] = close - close.shift(period)
            df[f'ROC_{period}'] = (close / close.shift(period) - 1) * 100

        # TRIX variants
        for period in [5, 8, 10, 14, 20, 28]:
            trix_val = self.tema(close, period).pct_change() * 10000
            df[f'TRIX_{period}'] = trix_val
            df[f'TRIX_SIGNAL_{period}'] = self.ema(trix_val, 9)

        # 10. HARMONIC PATTERNS (20 features) - NEW[10]
        harmonic_patterns = self.detect_harmonic_patterns(high, low, close)
        for pattern_name, pattern_values in harmonic_patterns.items():
            df[pattern_name] = pattern_values

        # 11. ELLIOTT WAVE RATIOS (15 features) - NEW[10]
        elliott_ratios = self.elliott_wave_ratios(close)
        for ratio_name, ratio_values in elliott_ratios.items():
            df[ratio_name] = ratio_values

        # 12. ADVANCED CANDLESTICK PATTERNS (50+ features) - MASSIVELY ENHANCED[8]
        advanced_patterns = self.advanced_candlestick_patterns(open_price, high, low, close)
        for pattern_name, pattern_values in advanced_patterns.items():
            df[pattern_name] = pattern_values

        # Basic patterns (keeping existing ones)
        df['DOJI'] = (abs(close - open_price) <= (high - low) * 0.1).astype(int)
        df['HAMMER'] = ((df['LOWER_SHADOW'] > 2 * df['BODY']) &
                       (df['UPPER_SHADOW'] < df['BODY'])).astype(int)
        df['BULLISH_ENGULFING'] = ((close > open_price) &
                                  (close.shift(1) < open_price.shift(1)) &
                                  (close > open_price.shift(1)) &
                                  (open_price < close.shift(1))).astype(int)
        df['BEARISH_ENGULFING'] = ((close < open_price) &
                                  (close.shift(1) > open_price.shift(1)) &
                                  (close < open_price.shift(1)) &
                                  (open_price > close.shift(1))).astype(int)

        # 13. FIBONACCI LEVELS (20 features) - Enhanced
        for window in [8, 13, 21, 34, 55]:
            swing_high = high.rolling(window).max()
            swing_low = low.rolling(window).min()
            fib_range = swing_high - swing_low

            df[f'FIB_23_6_{window}'] = swing_high - 0.236 * fib_range
            df[f'FIB_38_2_{window}'] = swing_high - 0.382 * fib_range
            df[f'FIB_50_0_{window}'] = swing_high - 0.500 * fib_range
            df[f'FIB_61_8_{window}'] = swing_high - 0.618 * fib_range
            df[f'FIB_78_6_{window}'] = swing_high - 0.786 * fib_range

            # Fibonacci retracement percentages[10]
            df[f'FIB_RETRACEMENT_{window}'] = (close - swing_low) / fib_range

        # 14. CORRELATION INDICATORS (15 features) - Enhanced
        for period in [5, 10, 14, 20, 30]:
            df[f'PRICE_VOLUME_CORR_{period}'] = close.rolling(period).corr(volume)
            df[f'HIGH_LOW_CORR_{period}'] = high.rolling(period).corr(low)
            df[f'OPEN_CLOSE_CORR_{period}'] = open_price.rolling(period).corr(close)

        # Cross-correlations
        df['RANGE_VOLUME_CORR_10'] = df['RANGE'].rolling(10).corr(volume)
        df['RANGE_VOLUME_CORR_20'] = df['RANGE'].rolling(20).corr(volume)
        df['BODY_VOLUME_CORR_10'] = df['BODY'].rolling(10).corr(volume)

        # Clean up inf/nan values
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)

        # Optimize data types
        df = self.optimize_dtypes(df)

        total_features = len(df.columns)
        technical_features = total_features - len(required_cols) - 2  # -2 for Target and Crypto_Pair
        print(f"✅ Generated {total_features} total columns ({technical_features} technical indicators)")

        return df

    def prepare_5min_data(self, df):
        """Prepare already 5-minute bar data - no conversion needed"""
        try:
            df = df.copy()
            # Handle both timestamp formats: epoch ms and datetime string
            if 'time' in df.columns:
                # New format: time,open,high,low,close,volume with datetime strings
                df['Datetime'] = pd.to_datetime(df['time'])
                df = df.drop('time', axis=1)
            elif 'Timestamp' in df.columns:
                # Old format: Timestamp as epoch ms
                df['Datetime'] = pd.to_datetime(df['Timestamp'], unit='ms')
                df = df.drop('Timestamp', axis=1)
            else:
                return None

            df.set_index('Datetime', inplace=True)
            df = df.dropna()
            return df if len(df) > 0 else None
        except Exception as e:
            print(f"    ⚠️ Error in prepare_5min_data: {e}")
            return None

    def create_target_variable(self, df, profit_threshold=0.015, loss_threshold=-0.015):
        df = df.copy()
        future_returns = []
        for i in range(len(df)):
            if i >= len(df) - 5:
                future_returns.append(0)
                continue
            current_price = df['Close'].iloc[i]
            target = 0
            for j in range(1, 6):
                if i + j < len(df):
                    future_price = df['Close'].iloc[i + j]
                    return_pct = (future_price - current_price) / current_price
                    if return_pct >= profit_threshold:
                        target = 1
                        break
                    elif return_pct <= loss_threshold:
                        target = 0
                        break
            future_returns.append(target)
        df['Target'] = future_returns
        return df

    # [Rest of your methods remain unchanged...]

    """EXACT SAME technical analyzer used in training - ensures feature consistency"""

    def __init__(self, sequence_length=60):
        self.sequence_length = sequence_length
        self.scaler = StandardScaler()

    def prepare_5min_data(self, df):
        """Prepare 5-minute bar data"""
        try:
            df = df.copy()
            # Ensure proper column names for trading bot data
            if 'close' in df.columns:
                # Trading bot format: lowercase
                df.columns = [col.capitalize() for col in df.columns]

            # Handle datetime index
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)

            df = df.dropna()
            return df if len(df) > 0 else None
        except Exception as e:
            print(f"⚠️ Error in prepare_5min_data: {e}")
            return None

    # Basic calculation functions - EXACT SAME AS TRAINING
    def sma(self, data, period): return data.rolling(window=period).mean()
    def ema(self, data, period): return data.ewm(span=period).mean()
    def wma(self, data, period):
        weights = np.arange(1, period + 1)
        return data.rolling(period).apply(lambda x: np.dot(x, weights) / weights.sum(), raw=True)
    def tema(self, data, period):
        ema1 = self.ema(data, period)
        ema2 = self.ema(ema1, period)
        ema3 = self.ema(ema2, period)
        return 3 * (ema1 - ema2) + ema3
    def dema(self, data, period):
        ema1 = self.ema(data, period)
        ema2 = self.ema(ema1, period)
        return 2 * ema1 - ema2
    def hma(self, data, period):
        wma1 = self.wma(data, period // 2) * 2
        wma2 = self.wma(data, period)
        raw_hma = wma1 - wma2
        return self.wma(raw_hma, int(np.sqrt(period)))

    def kama(self, data, period=10, fast_sc=2, slow_sc=30):
        change = abs(data.diff(period))
        volatility = data.diff().abs().rolling(period).sum()
        er = change / volatility
        sc = ((er * (2.0 / (fast_sc + 1) - 2.0 / (slow_sc + 1.0)) + 2 / (slow_sc + 1.0)) ** 2.0)
        kama = data.copy()
        for i in range(period, len(data)):
            kama.iloc[i] = kama.iloc[i-1] + sc.iloc[i] * (data.iloc[i] - kama.iloc[i-1])
        return kama

    def rsi(self, data, period=14):
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def stochastic(self, high, low, close, k_period=14, d_period=3):
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        return k_percent, d_percent

    def williams_r(self, high, low, close, period=14):
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        return -100 * (highest_high - close) / (highest_high - lowest_low)

    def cci(self, high, low, close, period=20):
        tp = (high + low + close) / 3
        sma_tp = tp.rolling(window=period).mean()
        mad = tp.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
        return (tp - sma_tp) / (0.015 * mad)

    def true_range(self, high, low, close):
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        return pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

    def atr(self, high, low, close, period=14):
        return self.true_range(high, low, close).rolling(window=period).mean()

    def adx(self, high, low, close, period=14):
        tr = self.true_range(high, low, close)
        plus_dm = high.diff()
        minus_dm = -low.diff()
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0
        plus_di = 100 * (plus_dm.rolling(period).mean() / tr.rolling(period).mean())
        minus_di = 100 * (minus_dm.rolling(period).mean() / tr.rolling(period).mean())
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(period).mean()
        return adx, plus_di, minus_di

    def macd(self, data, fast=12, slow=26, signal=9):
        ema_fast = self.ema(data, fast)
        ema_slow = self.ema(data, slow)
        macd = ema_fast - ema_slow
        signal_line = self.ema(macd, signal)
        histogram = macd - signal_line
        return macd, signal_line, histogram

    def bollinger_bands(self, data, period=20, std_dev=2):
        sma = self.sma(data, period)
        std = data.rolling(window=period).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower

    def mfi(self, high, low, close, volume, period=14):
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0).rolling(window=period).sum()
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0).rolling(window=period).sum()
        return 100 - (100 / (1 + positive_flow / negative_flow))

    def obv(self, close, volume):
        return (volume * np.sign(close.diff())).cumsum()

    def vwap(self, high, low, close, volume):
        typical_price = (high + low + close) / 3
        return (typical_price * volume).cumsum() / volume.cumsum()

    def optimize_dtypes(self, df):
        """Optimize data types to save memory"""
        for col in df.columns:
            if df[col].dtype == 'float64':
                df[col] = df[col].astype('float32')
        return df



    def create_multi_horizon_target(self, df, horizon=10, target_type="regression"):
        """Create multi-step prediction targets - same as training"""
        try:
            close_prices = df['Close'].values

            for step in range(1, horizon + 1):
                if target_type == "regression":
                    # Price return prediction
                    future_returns = np.roll(close_prices, -step) / close_prices - 1
                    future_returns[-step:] = np.nan
                    df[f'Target_Step_{step}'] = future_returns
                else:
                    # Classification: 1 if price goes up, 0 if down
                    future_direction = (np.roll(close_prices, -step) > close_prices).astype(int)
                    future_direction[-step:] = np.nan
                    df[f'Target_Step_{step}'] = future_direction

            # Remove rows where we can't predict
            df = df.iloc[:-horizon].copy()

            return df

        except Exception as e:
            print(f"❌ Error creating targets: {e}")
            return None

# ========================================================================================
# MAIN TRADING BOT CLASS
# ========================================================================================

class CryptoTradingBot:
    """Advanced ML-powered crypto trading bot"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.setup_logging()
        self.setup_binance_client()
        self.load_models_and_scalers()
        
        # Trading state
        self.active_positions: Dict[str, TradePosition] = {}
        self.current_balance = config.max_budget
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0
        
        # Data storage
        self.market_data: Dict[str, pd.DataFrame] = {}
        self.predictions_history: List[PredictionResult] = []
        
        # Technical indicators - SAME AS TRAINING
        self.indicators = MaximumTechnicalAnalyzer(sequence_length=config.sequence_length)
        
        # Threading
        self.data_lock = Lock()
        self.trading_lock = Lock()
        self.running = True
        
        self.logger.info("🚀 Crypto Trading Bot initialized successfully!")
        self.logger.info(f"💰 Budget: ${self.current_balance}")
        self.logger.info(f"📊 Monitoring pairs: {self.config.crypto_pairs}")
        self.logger.info(f"🎯 Prediction threshold: {self.config.prediction_threshold}%")
    
    def setup_logging(self):
        """Setup comprehensive logging with Unicode support"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

        # Create file handler with UTF-8 encoding
        file_handler = logging.FileHandler(
            f'trading_bot_{datetime.now().strftime("%Y%m%d")}.log',
            encoding='utf-8'
        )
        file_handler.setFormatter(logging.Formatter(log_format))

        # Create console handler with UTF-8 encoding
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(log_format))

        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            handlers=[file_handler, console_handler]
        )
        self.logger = logging.getLogger('TradingBot')
    
    def setup_binance_client(self):
        """Initialize Binance client"""
        try:
            if self.config.testnet:
                self.client = Client(
                    self.config.api_key, 
                    self.config.api_secret,
                    testnet=True
                )
                self.logger.info("🧪 Connected to Binance Testnet")
            else:
                self.client = Client(self.config.api_key, self.config.api_secret)
                self.logger.info("🔴 Connected to Binance Live Trading")
            
            # Test connection
            account_info = self.client.get_account()
            self.logger.info("✅ Binance API connection successful")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to connect to Binance API: {e}")
            raise
    
    def load_models_and_scalers(self):
        """Load trained models and scalers for each crypto pair"""
        self.models = {}
        self.scalers = {}
        
        for symbol in self.config.crypto_pairs:
            try:
                # Load model
                model_path = os.path.join(
                    self.config.models_path, 
                    f"final_{symbol}_{self.config.prediction_horizon}steps_model.keras"
                )
                if os.path.exists(model_path):
                    self.models[symbol] = tf.keras.models.load_model(model_path)
                    self.logger.info(f"✅ Loaded model for {symbol}")
                else:
                    self.logger.warning(f"⚠️ Model not found for {symbol}: {model_path}")
                    continue
                
                # Load scaler
                scaler_path = os.path.join(
                    self.config.scalers_path,
                    f"{symbol}_scaler.joblib"
                )
                if os.path.exists(scaler_path):
                    self.scalers[symbol] = joblib.load(scaler_path)
                    self.logger.info(f"✅ Loaded scaler for {symbol}")
                else:
                    self.logger.warning(f"⚠️ Scaler not found for {symbol}: {scaler_path}")
                    # Remove model if scaler is missing
                    del self.models[symbol]
                    
            except Exception as e:
                self.logger.error(f"❌ Failed to load model/scaler for {symbol}: {e}")
                if symbol in self.models:
                    del self.models[symbol]
                if symbol in self.scalers:
                    del self.scalers[symbol]
        
        self.logger.info(f"📊 Successfully loaded {len(self.models)} model-scaler pairs")
        
        # Update crypto pairs to only include those with models
        self.config.crypto_pairs = list(self.models.keys())
        
        if not self.models:
            raise ValueError("❌ No models loaded! Cannot proceed with trading.")

    def get_historical_data(self, symbol: str, interval: str = '5m', limit: int = 500) -> pd.DataFrame:
        """Fetch historical kline data from Binance"""
        try:
            klines = self.client.get_klines(symbol=symbol, interval=interval, limit=limit)

            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])

            # Convert to proper data types
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            df.set_index('timestamp', inplace=True)
            df = df[['open', 'high', 'low', 'close', 'volume']]

            return df

        except Exception as e:
            self.logger.error(f"❌ Failed to fetch data for {symbol}: {e}")
            return pd.DataFrame()

    def update_market_data(self):
        """Update market data for all monitored pairs"""
        with self.data_lock:
            for symbol in self.config.crypto_pairs:
                try:
                    df = self.get_historical_data(symbol)
                    if not df.empty:
                        # Prepare data for technical analysis (EXACT SAME AS TRAINING)
                        df_prepared = self.indicators.prepare_5min_data(df)
                        if df_prepared is not None:
                            # Add MAXIMUM technical indicators (same as training)
                            df_with_indicators = self.indicators.add_maximum_technical_indicators(df_prepared)
                            if df_with_indicators is not None:
                                # Add dummy target columns (same as training process)
                                # This ensures feature selection matches training exactly
                                for step in range(1, self.config.prediction_horizon + 1):
                                    df_with_indicators[f'Target_Step_{step}'] = 0.0

                                # Add Crypto_Pair column (same as training)
                                df_with_indicators['Crypto_Pair'] = symbol

                                self.market_data[symbol] = df_with_indicators
                                self.logger.debug(f"📊 Updated data for {symbol}: {len(df_with_indicators)} candles with {len(df_with_indicators.columns)} features")
                            else:
                                self.logger.warning(f"⚠️ Failed to add indicators for {symbol}")
                        else:
                            self.logger.warning(f"⚠️ Failed to prepare data for {symbol}")
                    else:
                        self.logger.warning(f"⚠️ No data received for {symbol}")

                except Exception as e:
                    self.logger.error(f"❌ Error updating data for {symbol}: {e}")

    def prepare_features_for_prediction(self, df: pd.DataFrame, symbol: str) -> np.ndarray:
        """Prepare features for ML model prediction - SAME AS TRAINING"""
        try:
            # Get the last sequence_length rows
            if len(df) < self.config.sequence_length:
                self.logger.warning(f"⚠️ Insufficient data for {symbol}: {len(df)} < {self.config.sequence_length}")
                return None

            # Feature selection EXACTLY SAME AS TRAINING
            # From a1.py line 1500-1501: excl = set(target_cols) | {'Crypto_Pair'}
            target_cols = [col for col in df.columns if col.startswith('Target_Step_')]
            excl = set(target_cols) | {'Crypto_Pair'}
            feature_cols = [c for c in df.columns if c not in excl]

            self.logger.debug(f"🔧 Using {len(feature_cols)} features for {symbol} prediction")

            # Get the most recent data
            recent_data = df[feature_cols].tail(self.config.sequence_length)

            # Fill any remaining NaN values (same as training)
            recent_data = recent_data.fillna(method='ffill').fillna(method='bfill').fillna(0)

            # Replace infinite values (same as training)
            recent_data = recent_data.replace([np.inf, -np.inf], 0)

            # Get expected feature names from scaler (what the model was trained on)
            try:
                expected_features = self.scalers[symbol].feature_names_in_
                current_features = recent_data.columns.tolist()

                self.logger.info(f"📊 Expected features: {len(expected_features)}")
                self.logger.info(f"📊 Current features: {len(current_features)}")

                # Save current features to file for comparison
                with open(f'current_features_{symbol}.txt', 'w') as f:
                    f.write(f"Current features for {symbol} ({len(current_features)} total):\n")
                    for feature in sorted(current_features):
                        f.write(f"{feature}\n")

                # Check for missing features
                missing_features = set(expected_features) - set(current_features)
                extra_features = set(current_features) - set(expected_features)

                if missing_features:
                    self.logger.warning(f"⚠️ Missing features for {symbol}: {len(missing_features)} features")

                    # Log the first 20 missing features to understand the pattern
                    missing_list = sorted(list(missing_features))
                    self.logger.info(f"🔍 First 20 missing features: {missing_list[:20]}")

                    # Save full list to file for analysis
                    with open(f'missing_features_{symbol}.txt', 'w') as f:
                        f.write(f"Missing features for {symbol} ({len(missing_features)} total):\n")
                        for feature in missing_list:
                            f.write(f"{feature}\n")

                    self.logger.info(f"📝 Full missing features list saved to missing_features_{symbol}.txt")

                    # Add missing features with zeros
                    for feature in missing_features:
                        recent_data[feature] = 0.0

                if extra_features:
                    self.logger.debug(f"🔧 Extra features for {symbol}: {len(extra_features)} features (will be ignored)")

                # Reorder columns to match training order
                recent_data = recent_data[expected_features]

            except AttributeError:
                # Older sklearn version might not have feature_names_in_
                self.logger.debug(f"🔧 Using current features for {symbol} (sklearn version compatibility)")

            # Scale features using the crypto-specific scaler
            scaled_features = self.scalers[symbol].transform(recent_data)

            # Reshape for LSTM input: (1, sequence_length, n_features)
            features = scaled_features.reshape(1, self.config.sequence_length, -1)

            return features.astype(np.float32)

        except Exception as e:
            self.logger.error(f"❌ Error preparing features for {symbol}: {e}")
            import traceback
            traceback.print_exc()
            return None

    def make_prediction(self, symbol: str) -> Optional[PredictionResult]:
        """Make price prediction using trained model"""
        try:
            if symbol not in self.market_data or symbol not in self.models:
                return None

            df = self.market_data[symbol]
            if df.empty:
                return None

            # Prepare features
            features = self.prepare_features_for_prediction(df, symbol)
            if features is None:
                return None

            # Make prediction
            prediction = self.models[symbol].predict(features, verbose=0)
            predicted_returns = prediction[0]  # Shape: (prediction_horizon,)

            # Convert returns to actual prices
            current_price = float(df['Close'].iloc[-1])
            predicted_prices = []

            for i, return_pct in enumerate(predicted_returns):
                if i == 0:
                    predicted_price = current_price * (1 + return_pct)
                else:
                    predicted_price = predicted_prices[-1] * (1 + return_pct)
                predicted_prices.append(predicted_price)

            # Calculate gains: check best price within prediction horizon (more realistic for trading)
            final_predicted_price = predicted_prices[-1]
            max_predicted_price = max(predicted_prices)
            min_predicted_price = min(predicted_prices)

            # Calculate gain percentages
            final_gain = ((final_predicted_price - current_price) / current_price) * 100
            max_gain = ((max_predicted_price - current_price) / current_price) * 100
            min_gain = ((min_predicted_price - current_price) / current_price) * 100

            # Find step where maximum gain occurs
            max_gain_step = predicted_prices.index(max_predicted_price) + 1

            # Check if target gain is reached at any point during prediction horizon
            target_reached = max_gain >= self.config.prediction_threshold

            # Calculate confidence (simplified - based on prediction consistency)
            confidence = 1.0 - (np.std(predicted_returns) / (np.abs(np.mean(predicted_returns)) + 1e-8))
            confidence = max(0.0, min(1.0, confidence))

            result = PredictionResult(
                symbol=symbol,
                current_price=current_price,
                predicted_prices=predicted_prices,
                predicted_gain=final_gain,
                max_gain=max_gain,
                max_gain_step=max_gain_step,
                min_gain=min_gain,
                target_reached=target_reached,
                confidence=confidence,
                timestamp=datetime.now()
            )

            self.logger.debug(f"🔮 {symbol} Prediction: Final: {final_gain:.2f}%, Max: {max_gain:.2f}% (step {max_gain_step}), Target: {'✅' if target_reached else '❌'}")
            return result

        except Exception as e:
            self.logger.error(f"❌ Prediction error for {symbol}: {e}")
            return None

    def get_account_balance(self) -> float:
        """Get current USDT balance"""
        try:
            account = self.client.get_account()
            for balance in account['balances']:
                if balance['asset'] == 'USDT':
                    return float(balance['free'])
            return 0.0
        except Exception as e:
            self.logger.error(f"❌ Error getting balance: {e}")
            return 0.0

    def can_place_trade(self) -> bool:
        """Check if we can place a new trade based on risk management rules"""
        # Check if we have enough balance
        if self.current_balance < self.config.trade_amount:
            return False

        # Check concurrent trades limit
        active_count = len([pos for pos in self.active_positions.values() if pos.status == "ACTIVE"])
        if active_count >= self.config.max_concurrent_trades:
            return False

        # Check if we have enough balance for multiple trades
        if active_count > 0 and self.current_balance < self.config.min_budget_for_multiple_trades:
            return False

        return True

    def get_price_precision(self, symbol: str, price: float) -> float:
        """Round price to correct precision for the symbol"""
        try:
            exchange_info = self.client.get_exchange_info()
            symbol_info = next((s for s in exchange_info['symbols'] if s['symbol'] == symbol), None)

            if symbol_info:
                price_filter = next((f for f in symbol_info['filters'] if f['filterType'] == 'PRICE_FILTER'), None)
                if price_filter:
                    tick_size = float(price_filter['tickSize'])
                    return round(price / tick_size) * tick_size

            return round(price, 8)  # Default precision

        except Exception as e:
            self.logger.error(f"❌ Error getting price precision for {symbol}: {e}")
            return round(price, 8)

    def calculate_trade_quantity(self, symbol: str, price: float) -> float:
        """Calculate trade quantity based on trade amount"""
        try:
            # Get symbol info for precision
            symbol_info = self.client.get_symbol_info(symbol)
            step_size = None

            for filter_item in symbol_info['filters']:
                if filter_item['filterType'] == 'LOT_SIZE':
                    step_size = float(filter_item['stepSize'])
                    break

            if step_size is None:
                step_size = 0.001  # Default

            # Calculate quantity
            quantity = self.config.trade_amount / price

            # Round to proper precision
            precision = len(str(step_size).split('.')[-1].rstrip('0'))
            quantity = round(quantity, precision)

            return quantity

        except Exception as e:
            self.logger.error(f"❌ Error calculating quantity for {symbol}: {e}")
            return 0.0

    def place_buy_order(self, symbol: str, prediction: PredictionResult) -> Optional[TradePosition]:
        """Place a buy order with OCO sell orders"""
        try:
            current_price = prediction.current_price
            quantity = self.calculate_trade_quantity(symbol, current_price)

            if quantity <= 0:
                self.logger.error(f"❌ Invalid quantity for {symbol}: {quantity}")
                return None

            # Place market buy order
            buy_order = self.client.order_market_buy(
                symbol=symbol,
                quantity=quantity
            )

            if buy_order['status'] != 'FILLED':
                self.logger.error(f"❌ Buy order not filled for {symbol}")
                return None

            # Get actual fill price and quantity
            fill_price = float(buy_order['fills'][0]['price'])
            fill_quantity = float(buy_order['executedQty'])

            # Calculate OCO prices with proper precision
            take_profit_price = self.get_price_precision(symbol, fill_price * (1 + self.config.take_profit / 100))
            stop_loss_price = self.get_price_precision(symbol, fill_price * (1 - self.config.stop_loss / 100))
            stop_limit_price = self.get_price_precision(symbol, fill_price * (1 - self.config.max_loss / 100))

            # Place OCO sell order with correct parameters
            try:
                oco_order = self.client.create_oco_order(
                    symbol=symbol,
                    side='SELL',
                    quantity=fill_quantity,
                    price=f"{take_profit_price:.8f}",
                    stopPrice=f"{stop_loss_price:.8f}",
                    stopLimitPrice=f"{stop_limit_price:.8f}",
                    stopLimitTimeInForce='GTC',
                    aboveType='LIMIT_MAKER',  # Required parameter for OCO
                    belowType='STOP_LOSS_LIMIT'  # Required parameter for OCO
                )

                oco_order_id = oco_order['orderListId']

            except Exception as e:
                self.logger.error(f"❌ Failed to place OCO order for {symbol}: {e}")
                # Place simple limit sell order as fallback (price already has correct precision)
                try:
                    sell_order = self.client.order_limit_sell(
                        symbol=symbol,
                        quantity=fill_quantity,
                        price=f"{take_profit_price:.8f}"
                    )
                    oco_order_id = sell_order['orderId']
                except Exception as e2:
                    self.logger.error(f"❌ Failed to place fallback sell order: {e2}")
                    oco_order_id = None

            # Create position object
            position = TradePosition(
                symbol=symbol,
                side='BUY',
                quantity=fill_quantity,
                entry_price=fill_price,
                entry_time=datetime.now(),
                order_id=buy_order['orderId'],
                oco_order_id=oco_order_id,
                predicted_gain=prediction.predicted_gain,
                status='ACTIVE'
            )

            # Update trading state
            with self.trading_lock:
                self.active_positions[symbol] = position
                self.current_balance -= (fill_quantity * fill_price)
                self.total_trades += 1

            self.logger.info(f"🟢 BUY {symbol}: {fill_quantity:.6f} @ ${fill_price:.4f}")
            self.logger.info(f"🎯 Take Profit: ${take_profit_price:.4f}, Stop Loss: ${stop_loss_price:.4f}")
            self.logger.info(f"💰 Remaining Balance: ${self.current_balance:.2f}")

            return position

        except BinanceAPIException as e:
            self.logger.error(f"❌ Binance API error placing buy order for {symbol}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Error placing buy order for {symbol}: {e}")
            return None

    def update_positions(self):
        """Update status of active positions"""
        positions_to_remove = []

        with self.trading_lock:
            for symbol, position in self.active_positions.items():
                try:
                    if position.status != 'ACTIVE':
                        continue

                    # Check if OCO order is still active
                    if position.oco_order_id:
                        try:
                            # Check order status
                            orders = self.client.get_open_orders(symbol=symbol)
                            oco_active = any(order['orderId'] == position.oco_order_id for order in orders)

                            if not oco_active:
                                # OCO order executed, check which one
                                order_history = self.client.get_all_orders(symbol=symbol, limit=10)
                                for order in order_history:
                                    if order['orderId'] == position.oco_order_id and order['status'] == 'FILLED':
                                        # Calculate PnL
                                        exit_price = float(order['price'])
                                        pnl = (exit_price - position.entry_price) * position.quantity
                                        pnl_percent = ((exit_price - position.entry_price) / position.entry_price) * 100

                                        position.current_pnl = pnl
                                        position.status = 'CLOSED'

                                        # Update balance and stats
                                        self.current_balance += (position.quantity * exit_price)
                                        self.total_pnl += pnl

                                        if pnl > 0:
                                            self.successful_trades += 1
                                            self.logger.info(f"🟢 PROFIT {symbol}: +${pnl:.2f} ({pnl_percent:.2f}%)")
                                        else:
                                            self.logger.info(f"🔴 LOSS {symbol}: ${pnl:.2f} ({pnl_percent:.2f}%)")

                                        positions_to_remove.append(symbol)
                                        break

                        except Exception as e:
                            self.logger.error(f"❌ Error checking position status for {symbol}: {e}")

                    # Update current PnL for active positions
                    if position.status == 'ACTIVE':
                        try:
                            current_price = float(self.client.get_symbol_ticker(symbol=symbol)['price'])
                            unrealized_pnl = (current_price - position.entry_price) * position.quantity
                            position.current_pnl = unrealized_pnl
                        except:
                            pass

                except Exception as e:
                    self.logger.error(f"❌ Error updating position for {symbol}: {e}")

        # Remove closed positions
        for symbol in positions_to_remove:
            del self.active_positions[symbol]

    def analyze_and_trade(self):
        """Main trading logic - analyze predictions and execute trades"""
        try:
            if not self.can_place_trade():
                return

            # Get predictions for all pairs
            predictions = []
            for symbol in self.config.crypto_pairs:
                if symbol not in self.active_positions:  # Don't trade if already have position
                    prediction = self.make_prediction(symbol)
                    if prediction and prediction.target_reached:  # Use target_reached instead of final gain
                        predictions.append(prediction)
                        self.predictions_history.append(prediction)

            if not predictions:
                return

            # Sort by maximum gain within prediction horizon (highest first)
            predictions.sort(key=lambda x: x.max_gain, reverse=True)

            # Log all good predictions with detailed information
            for pred in predictions:
                self.logger.info(f"🔮 {pred.symbol}: Max gain {pred.max_gain:.2f}% at step {pred.max_gain_step}, Final: {pred.predicted_gain:.2f}% (confidence: {pred.confidence:.2f})")

            # Select best prediction that we can afford
            for prediction in predictions:
                if self.can_place_trade():
                    position = self.place_buy_order(prediction.symbol, prediction)
                    if position:
                        self.logger.info(f"✅ Trade executed for {prediction.symbol}")
                        break
                else:
                    break

        except Exception as e:
            self.logger.error(f"❌ Error in analyze_and_trade: {e}")

    def log_status(self):
        """Log current bot status"""
        active_count = len([pos for pos in self.active_positions.values() if pos.status == "ACTIVE"])
        total_unrealized_pnl = sum(pos.current_pnl for pos in self.active_positions.values() if pos.status == "ACTIVE")

        self.logger.info("=" * 60)
        self.logger.info(f"📊 TRADING BOT STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"💰 Current Balance: ${self.current_balance:.2f}")
        self.logger.info(f"📈 Total PnL: ${self.total_pnl:.2f}")
        self.logger.info(f"📊 Active Positions: {active_count}")
        self.logger.info(f"💹 Unrealized PnL: ${total_unrealized_pnl:.2f}")
        self.logger.info(f"🎯 Total Trades: {self.total_trades}")
        self.logger.info(f"✅ Successful Trades: {self.successful_trades}")

        if self.total_trades > 0:
            success_rate = (self.successful_trades / self.total_trades) * 100
            self.logger.info(f"📊 Success Rate: {success_rate:.1f}%")

        # Log active positions
        for symbol, pos in self.active_positions.items():
            if pos.status == "ACTIVE":
                pnl_percent = (pos.current_pnl / (pos.entry_price * pos.quantity)) * 100
                self.logger.info(f"   {symbol}: ${pos.current_pnl:.2f} ({pnl_percent:.2f}%)")

        self.logger.info("=" * 60)

    def save_state(self):
        """Save current trading state to file"""
        try:
            state = {
                'timestamp': datetime.now().isoformat(),
                'current_balance': self.current_balance,
                'total_pnl': self.total_pnl,
                'total_trades': self.total_trades,
                'successful_trades': self.successful_trades,
                'active_positions': {k: asdict(v) for k, v in self.active_positions.items()},
                'recent_predictions': [asdict(p) for p in self.predictions_history[-50:]]  # Last 50 predictions
            }

            with open(f'trading_state_{datetime.now().strftime("%Y%m%d")}.json', 'w') as f:
                json.dump(state, f, indent=2, default=str)

        except Exception as e:
            self.logger.error(f"❌ Error saving state: {e}")

    def emergency_exit(self):
        """Emergency exit - close all positions"""
        self.logger.warning("🚨 EMERGENCY EXIT - Closing all positions!")

        with self.trading_lock:
            for symbol, position in self.active_positions.items():
                if position.status == "ACTIVE":
                    try:
                        # Cancel existing orders
                        if position.oco_order_id:
                            try:
                                self.client.cancel_order(symbol=symbol, orderId=position.oco_order_id)
                            except:
                                pass

                        # Place market sell order
                        sell_order = self.client.order_market_sell(
                            symbol=symbol,
                            quantity=position.quantity
                        )

                        if sell_order['status'] == 'FILLED':
                            exit_price = float(sell_order['fills'][0]['price'])
                            pnl = (exit_price - position.entry_price) * position.quantity

                            position.current_pnl = pnl
                            position.status = 'EMERGENCY_CLOSED'

                            self.current_balance += (position.quantity * exit_price)
                            self.total_pnl += pnl

                            self.logger.warning(f"🚨 Emergency closed {symbol}: PnL ${pnl:.2f}")

                    except Exception as e:
                        self.logger.error(f"❌ Error in emergency exit for {symbol}: {e}")

    def run_test_mode(self, duration_hours: int = 24):
        """Run bot in test mode for specified duration"""
        self.logger.info(f"🧪 Starting TEST MODE for {duration_hours} hours")
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=duration_hours)

        try:
            while datetime.now() < end_time and self.running:
                # Update market data
                self.update_market_data()

                # Update positions
                self.update_positions()

                # Analyze and trade
                self.analyze_and_trade()

                # Log status every hour
                if datetime.now().minute == 0:
                    self.log_status()

                # Save state
                self.save_state()

                # Wait for next update
                time.sleep(self.config.update_interval)

        except KeyboardInterrupt:
            self.logger.info("🛑 Test mode interrupted by user")
        except Exception as e:
            self.logger.error(f"❌ Error in test mode: {e}")
        finally:
            self.emergency_exit()
            self.log_status()
            self.logger.info("🧪 Test mode completed")

    def run_live_mode(self):
        """Run bot in live trading mode"""
        self.logger.info("🔴 Starting LIVE TRADING MODE")
        self.logger.warning("⚠️ REAL MONEY AT RISK!")

        try:
            while self.running:
                # Update market data
                self.update_market_data()

                # Update positions
                self.update_positions()

                # Analyze and trade
                self.analyze_and_trade()

                # Log status every 30 minutes
                if datetime.now().minute % 30 == 0:
                    self.log_status()

                # Save state every update
                self.save_state()

                # Wait for next update
                time.sleep(self.config.update_interval)

        except KeyboardInterrupt:
            self.logger.info("🛑 Live trading interrupted by user")
        except Exception as e:
            self.logger.error(f"❌ Error in live trading: {e}")
        finally:
            self.emergency_exit()
            self.log_status()
            self.logger.info("🔴 Live trading stopped")

    def stop(self):
        """Stop the trading bot"""
        self.running = False
        self.logger.info("🛑 Trading bot stop signal received")

# ========================================================================================
# CONFIGURATION AND UTILITY FUNCTIONS
# ========================================================================================

def load_config_from_file(config_file: str = "trading_config.ini") -> TradingConfig:
    """Load configuration from INI file"""
    config = configparser.ConfigParser()

    if os.path.exists(config_file):
        config.read(config_file)

        return TradingConfig(
            api_key=config.get('binance', 'api_key', fallback=''),
            api_secret=config.get('binance', 'api_secret', fallback=''),
            testnet=config.getboolean('binance', 'testnet', fallback=True),

            max_budget=config.getfloat('trading', 'max_budget', fallback=30.0),
            trade_amount=config.getfloat('trading', 'trade_amount', fallback=14.0),
            max_concurrent_trades=config.getint('trading', 'max_concurrent_trades', fallback=2),
            min_budget_for_multiple_trades=config.getfloat('trading', 'min_budget_for_multiple_trades', fallback=28.0),

            prediction_threshold=config.getfloat('ml', 'prediction_threshold', fallback=1.9),
            take_profit=config.getfloat('ml', 'take_profit', fallback=1.2),
            stop_loss=config.getfloat('ml', 'stop_loss', fallback=1.5),
            max_loss=config.getfloat('ml', 'max_loss', fallback=1.6),

            models_path=config.get('paths', 'models_path', fallback='/content/drive/MyDrive/crypto_models_separate'),
            scalers_path=config.get('paths', 'scalers_path', fallback='/content/drive/MyDrive/crypto_scalers'),

            crypto_pairs=config.get('trading', 'crypto_pairs', fallback='BTCUSDT,ETHUSDT,ADAUSDT,BNBUSDT,SOLUSDT').split(',')
        )
    else:
        # Create default config file
        create_default_config(config_file)
        return TradingConfig()

def create_default_config(config_file: str = "trading_config.ini"):
    """Create default configuration file"""
    config = configparser.ConfigParser()

    config['binance'] = {
        'api_key': 'YOUR_BINANCE_API_KEY',
        'api_secret': 'YOUR_BINANCE_API_SECRET',
        'testnet': 'True'
    }

    config['trading'] = {
        'max_budget': '30.0',
        'trade_amount': '14.0',
        'max_concurrent_trades': '2',
        'min_budget_for_multiple_trades': '28.0',
        'crypto_pairs': 'BTCUSDT,ETHUSDT,ADAUSDT,BNBUSDT,SOLUSDT'
    }

    config['ml'] = {
        'prediction_threshold': '1.9',
        'take_profit': '1.2',
        'stop_loss': '1.5',
        'max_loss': '1.6'
    }

    config['paths'] = {
        'models_path': '/content/drive/MyDrive/crypto_models_separate',
        'scalers_path': '/content/drive/MyDrive/crypto_scalers'
    }

    with open(config_file, 'w') as f:
        config.write(f)

    print(f"✅ Created default config file: {config_file}")
    print("⚠️ Please edit the config file with your Binance API credentials!")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\n🛑 Shutdown signal received. Stopping bot...")
    global bot
    if 'bot' in globals():
        bot.stop()
    sys.exit(0)

# ========================================================================================
# MAIN EXECUTION
# ========================================================================================

def main():
    """Main execution function"""
    print("🚀 Advanced Crypto Trading Bot with ML Predictions")
    print("=" * 60)

    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Load configuration
    try:
        config = load_config_from_file()
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return

    # Validate API credentials
    if not config.api_key or config.api_key == 'YOUR_BINANCE_API_KEY':
        print("❌ Please set your Binance API credentials in trading_config.ini")
        return

    # Initialize bot
    try:
        global bot
        bot = CryptoTradingBot(config)
        print("✅ Trading bot initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing bot: {e}")
        return

    # Choose mode
    print("\n📋 Select trading mode:")
    print("1. Test Mode (24 hours)")
    print("2. Test Mode (Custom duration)")
    print("3. Live Trading Mode")
    print("4. Exit")

    try:
        choice = input("\nEnter your choice (1-4): ").strip()

        if choice == '1':
            bot.run_test_mode(24)
        elif choice == '2':
            hours = int(input("Enter duration in hours: "))
            bot.run_test_mode(hours)
        elif choice == '3':
            confirm = input("⚠️ Are you sure you want to start LIVE trading? (yes/no): ").strip().lower()
            if confirm == 'yes':
                bot.run_live_mode()
            else:
                print("Live trading cancelled.")
        elif choice == '4':
            print("Exiting...")
        else:
            print("Invalid choice.")

    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if 'bot' in globals():
            bot.stop()

if __name__ == "__main__":
    main()

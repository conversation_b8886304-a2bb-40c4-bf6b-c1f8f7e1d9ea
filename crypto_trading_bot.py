#!/usr/bin/env python3
"""
Advanced Crypto Trading Bot with ML Predictions
Integrates with trained crypto-specific models from a1.py
Supports both test and live trading modes with comprehensive risk management
"""

import os
import json
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# ML and Data Processing
import tensorflow as tf
import joblib
from sklearn.preprocessing import StandardScaler

# Binance API
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException

# Configuration
import configparser
from dataclasses import dataclass, asdict
from threading import Thread, Lock
import asyncio
import signal
import sys

# ========================================================================================
# CONFIGURATION AND DATA CLASSES
# ========================================================================================

@dataclass
class TradingConfig:
    """Trading bot configuration"""
    # API Configuration
    api_key: str = "GWf3NmhOQi9q2KIhDLcugL1smupKLfwSzlSmY3TwexJqNf4ehDpXs4qy2eeZYviW"
    api_secret: str = "gJCCFfWoUCzDFfs9b6JCH6Q6yMg3vKSqxK5k5EnQ4rwHJXT6jPBvnyVaaJky14pC"
    testnet: bool = True
    
    # Trading Parameters
    max_budget: float = 30.0
    trade_amount: float = 14.0
    max_concurrent_trades: int = 2
    min_budget_for_multiple_trades: float = 28.0
    
    # ML Prediction Parameters
    prediction_threshold: float = 1.9  # Minimum predicted gain % to trigger buy
    take_profit: float = 1.2  # Take profit %
    stop_loss: float = 1.5   # Stop loss %
    max_loss: float = 1.6    # Maximum loss % (emergency exit)
    
    # Technical Parameters
    trading_fee: float = 0.1  # Trading fee %
    prediction_horizon: int = 10  # Number of future steps to predict
    sequence_length: int = 60    # Input sequence length for models
    
    # Monitoring Parameters
    update_interval: int = 300   # Data update interval in seconds (5 minutes)
    log_level: str = "INFO"
    
    # Model Paths
    models_path: str = "models"
    scalers_path: str = "scalers"
    
    # Crypto Pairs to Monitor
    crypto_pairs: List[str] = None
    
    def __post_init__(self):
        if self.crypto_pairs is None:
            self.crypto_pairs = ["XRPUSDT"] 

@dataclass
class TradePosition:
    """Represents an active trading position"""
    symbol: str
    side: str  # 'BUY' or 'SELL'
    quantity: float
    entry_price: float
    entry_time: datetime
    order_id: str
    oco_order_id: Optional[str] = None
    predicted_gain: float = 0.0
    current_pnl: float = 0.0
    status: str = "ACTIVE"  # ACTIVE, CLOSED, STOPPED

@dataclass
class PredictionResult:
    """ML model prediction result"""
    symbol: str
    current_price: float
    predicted_prices: List[float]
    predicted_gain: float
    confidence: float
    timestamp: datetime

# ========================================================================================
# MAXIMUM TECHNICAL ANALYZER (Same as training in a1.py)
# ========================================================================================

class MaximumTechnicalAnalyzer:
    """EXACT SAME technical analyzer used in training - ensures feature consistency"""

    def __init__(self, sequence_length=60):
        self.sequence_length = sequence_length

    def prepare_5min_data(self, df):
        """Prepare 5-minute bar data"""
        try:
            df = df.copy()
            # Ensure proper column names for trading bot data
            if 'close' in df.columns:
                # Trading bot format: lowercase
                df.columns = [col.capitalize() for col in df.columns]

            # Handle datetime index
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)

            df = df.dropna()
            return df if len(df) > 0 else None
        except Exception as e:
            print(f"⚠️ Error in prepare_5min_data: {e}")
            return None

    def add_maximum_technical_indicators(self, df):
        """Add MAXIMUM 300+ technical indicators - SAME AS TRAINING"""
        try:
            print(f"🔄 Adding MAXIMUM technical indicators (300+)...")

            # Basic OHLCV
            open_price = df['Open'].astype(float)
            high = df['High'].astype(float)
            low = df['Low'].astype(float)
            close = df['Close'].astype(float)
            volume = df['Volume'].astype(float)

            # Price-based features
            df['hl_ratio'] = high / low
            df['oc_ratio'] = open_price / close
            df['hc_ratio'] = high / close
            df['lc_ratio'] = low / close
            df['price_range'] = (high - low) / close
            df['body_size'] = abs(close - open_price) / close
            df['upper_shadow'] = (high - np.maximum(open_price, close)) / close
            df['lower_shadow'] = (np.minimum(open_price, close) - low) / close

            # Returns and changes
            for period in [1, 2, 3, 5, 10, 20]:
                df[f'return_{period}'] = close.pct_change(period)
                df[f'log_return_{period}'] = np.log(close / close.shift(period))
                df[f'price_change_{period}'] = (close - close.shift(period)) / close.shift(period)

            # Moving averages (comprehensive)
            ma_periods = [3, 5, 7, 10, 14, 20, 21, 30, 50, 100, 200]
            for period in ma_periods:
                # Simple Moving Average
                df[f'sma_{period}'] = close.rolling(window=period).mean()
                df[f'sma_{period}_ratio'] = close / df[f'sma_{period}']

                # Exponential Moving Average
                df[f'ema_{period}'] = close.ewm(span=period).mean()
                df[f'ema_{period}_ratio'] = close / df[f'ema_{period}']

                # Volume moving averages
                df[f'volume_sma_{period}'] = volume.rolling(window=period).mean()
                if period <= 50:  # Avoid too many volume features
                    df[f'volume_ratio_{period}'] = volume / df[f'volume_sma_{period}']

            # RSI variations
            for period in [7, 14, 21, 30]:
                delta = close.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                df[f'rsi_{period}'] = 100 - (100 / (1 + rs))

            # Bollinger Bands variations
            for period in [10, 20, 30]:
                for std_mult in [1.5, 2.0, 2.5]:
                    sma = close.rolling(window=period).mean()
                    std = close.rolling(window=period).std()
                    bb_upper = sma + (std * std_mult)
                    bb_lower = sma - (std * std_mult)

                    df[f'bb_upper_{period}_{std_mult}'] = bb_upper
                    df[f'bb_lower_{period}_{std_mult}'] = bb_lower
                    df[f'bb_width_{period}_{std_mult}'] = (bb_upper - bb_lower) / sma
                    df[f'bb_position_{period}_{std_mult}'] = (close - bb_lower) / (bb_upper - bb_lower)

            # MACD variations
            for fast, slow, signal in [(12, 26, 9), (5, 35, 5), (8, 21, 5)]:
                ema_fast = close.ewm(span=fast).mean()
                ema_slow = close.ewm(span=slow).mean()
                macd = ema_fast - ema_slow
                macd_signal = macd.ewm(span=signal).mean()
                macd_histogram = macd - macd_signal

                df[f'macd_{fast}_{slow}'] = macd
                df[f'macd_signal_{fast}_{slow}_{signal}'] = macd_signal
                df[f'macd_histogram_{fast}_{slow}_{signal}'] = macd_histogram

            # Stochastic Oscillator
            for k_period, d_period in [(14, 3), (5, 3), (21, 5)]:
                lowest_low = low.rolling(window=k_period).min()
                highest_high = high.rolling(window=k_period).max()
                k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
                d_percent = k_percent.rolling(window=d_period).mean()

                df[f'stoch_k_{k_period}'] = k_percent
                df[f'stoch_d_{k_period}_{d_period}'] = d_percent

            # Williams %R
            for period in [14, 21]:
                highest_high = high.rolling(window=period).max()
                lowest_low = low.rolling(window=period).min()
                df[f'williams_r_{period}'] = -100 * (highest_high - close) / (highest_high - lowest_low)

            # Commodity Channel Index (CCI)
            for period in [14, 20]:
                tp = (high + low + close) / 3
                sma_tp = tp.rolling(window=period).mean()
                mad = tp.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
                df[f'cci_{period}'] = (tp - sma_tp) / (0.015 * mad)

            # Average True Range (ATR)
            for period in [7, 14, 21]:
                tr1 = high - low
                tr2 = abs(high - close.shift(1))
                tr3 = abs(low - close.shift(1))
                true_range = np.maximum(tr1, np.maximum(tr2, tr3))
                df[f'atr_{period}'] = true_range.rolling(window=period).mean()
                df[f'atr_ratio_{period}'] = df[f'atr_{period}'] / close

            # Money Flow Index
            for period in [14, 21]:
                typical_price = (high + low + close) / 3
                money_flow = typical_price * volume
                positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0).rolling(window=period).sum()
                negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0).rolling(window=period).sum()
                mfi = 100 - (100 / (1 + positive_flow / negative_flow))
                df[f'mfi_{period}'] = mfi

            # On-Balance Volume
            obv = (volume * np.sign(close.diff())).cumsum()
            df['obv'] = obv
            df['obv_sma_10'] = obv.rolling(window=10).mean()
            df['obv_ema_10'] = obv.ewm(span=10).mean()

            # Volume Price Trend
            vpt = (volume * (close.pct_change())).cumsum()
            df['vpt'] = vpt

            # Accumulation/Distribution Line
            clv = ((close - low) - (high - close)) / (high - low)
            ad_line = (clv * volume).cumsum()
            df['ad_line'] = ad_line

            # Price and Volume Momentum
            for period in [5, 10, 20]:
                df[f'price_momentum_{period}'] = close - close.shift(period)
                df[f'volume_momentum_{period}'] = volume - volume.shift(period)
                df[f'price_acceleration_{period}'] = df[f'price_momentum_{period}'] - df[f'price_momentum_{period}'].shift(1)

            # Volatility measures
            for period in [5, 10, 20, 30]:
                df[f'volatility_{period}'] = close.rolling(window=period).std()
                df[f'volatility_ratio_{period}'] = df[f'volatility_{period}'] / close

            # Support and Resistance levels
            for period in [5, 10, 20]:
                df[f'support_{period}'] = low.rolling(window=period).min()
                df[f'resistance_{period}'] = high.rolling(window=period).max()
                df[f'support_distance_{period}'] = (close - df[f'support_{period}']) / close
                df[f'resistance_distance_{period}'] = (df[f'resistance_{period}'] - close) / close

            # Trend indicators
            for period in [10, 20, 50]:
                df[f'trend_strength_{period}'] = (close - close.shift(period)) / close.shift(period)
                df[f'trend_consistency_{period}'] = (close > close.shift(1)).rolling(window=period).mean()

            # Fill NaN values
            df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)

            # Remove infinite values
            df = df.replace([np.inf, -np.inf], 0)

            total_indicators = len(df.columns) - 5  # Subtract OHLCV columns
            print(f"✅ Generated {len(df.columns)} total columns ({total_indicators} technical indicators)")

            return df

        except Exception as e:
            print(f"❌ Error adding maximum technical indicators: {e}")
            import traceback
            traceback.print_exc()
            return None

    def create_multi_horizon_target(self, df, horizon=10, target_type="regression"):
        """Create multi-step prediction targets - same as training"""
        try:
            close_prices = df['Close'].values

            for step in range(1, horizon + 1):
                if target_type == "regression":
                    # Price return prediction
                    future_returns = np.roll(close_prices, -step) / close_prices - 1
                    future_returns[-step:] = np.nan
                    df[f'Target_Step_{step}'] = future_returns
                else:
                    # Classification: 1 if price goes up, 0 if down
                    future_direction = (np.roll(close_prices, -step) > close_prices).astype(int)
                    future_direction[-step:] = np.nan
                    df[f'Target_Step_{step}'] = future_direction

            # Remove rows where we can't predict
            df = df.iloc[:-horizon].copy()

            return df

        except Exception as e:
            print(f"❌ Error creating targets: {e}")
            return None

# ========================================================================================
# MAIN TRADING BOT CLASS
# ========================================================================================

class CryptoTradingBot:
    """Advanced ML-powered crypto trading bot"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.setup_logging()
        self.setup_binance_client()
        self.load_models_and_scalers()
        
        # Trading state
        self.active_positions: Dict[str, TradePosition] = {}
        self.current_balance = config.max_budget
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0
        
        # Data storage
        self.market_data: Dict[str, pd.DataFrame] = {}
        self.predictions_history: List[PredictionResult] = []
        
        # Technical indicators - SAME AS TRAINING
        self.indicators = MaximumTechnicalAnalyzer(sequence_length=config.sequence_length)
        
        # Threading
        self.data_lock = Lock()
        self.trading_lock = Lock()
        self.running = True
        
        self.logger.info("🚀 Crypto Trading Bot initialized successfully!")
        self.logger.info(f"💰 Budget: ${self.current_balance}")
        self.logger.info(f"📊 Monitoring pairs: {self.config.crypto_pairs}")
        self.logger.info(f"🎯 Prediction threshold: {self.config.prediction_threshold}%")
    
    def setup_logging(self):
        """Setup comprehensive logging"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format=log_format,
            handlers=[
                logging.FileHandler(f'trading_bot_{datetime.now().strftime("%Y%m%d")}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('TradingBot')
    
    def setup_binance_client(self):
        """Initialize Binance client"""
        try:
            if self.config.testnet:
                self.client = Client(
                    self.config.api_key, 
                    self.config.api_secret,
                    testnet=True
                )
                self.logger.info("🧪 Connected to Binance Testnet")
            else:
                self.client = Client(self.config.api_key, self.config.api_secret)
                self.logger.info("🔴 Connected to Binance Live Trading")
            
            # Test connection
            account_info = self.client.get_account()
            self.logger.info("✅ Binance API connection successful")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to connect to Binance API: {e}")
            raise
    
    def load_models_and_scalers(self):
        """Load trained models and scalers for each crypto pair"""
        self.models = {}
        self.scalers = {}
        
        for symbol in self.config.crypto_pairs:
            try:
                # Load model
                model_path = os.path.join(
                    self.config.models_path, 
                    f"final_{symbol}_{self.config.prediction_horizon}steps_model.keras"
                )
                if os.path.exists(model_path):
                    self.models[symbol] = tf.keras.models.load_model(model_path)
                    self.logger.info(f"✅ Loaded model for {symbol}")
                else:
                    self.logger.warning(f"⚠️ Model not found for {symbol}: {model_path}")
                    continue
                
                # Load scaler
                scaler_path = os.path.join(
                    self.config.scalers_path,
                    f"{symbol}_scaler.joblib"
                )
                if os.path.exists(scaler_path):
                    self.scalers[symbol] = joblib.load(scaler_path)
                    self.logger.info(f"✅ Loaded scaler for {symbol}")
                else:
                    self.logger.warning(f"⚠️ Scaler not found for {symbol}: {scaler_path}")
                    # Remove model if scaler is missing
                    del self.models[symbol]
                    
            except Exception as e:
                self.logger.error(f"❌ Failed to load model/scaler for {symbol}: {e}")
                if symbol in self.models:
                    del self.models[symbol]
                if symbol in self.scalers:
                    del self.scalers[symbol]
        
        self.logger.info(f"📊 Successfully loaded {len(self.models)} model-scaler pairs")
        
        # Update crypto pairs to only include those with models
        self.config.crypto_pairs = list(self.models.keys())
        
        if not self.models:
            raise ValueError("❌ No models loaded! Cannot proceed with trading.")

    def get_historical_data(self, symbol: str, interval: str = '5m', limit: int = 500) -> pd.DataFrame:
        """Fetch historical kline data from Binance"""
        try:
            klines = self.client.get_klines(symbol=symbol, interval=interval, limit=limit)

            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])

            # Convert to proper data types
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            df.set_index('timestamp', inplace=True)
            df = df[['open', 'high', 'low', 'close', 'volume']]

            return df

        except Exception as e:
            self.logger.error(f"❌ Failed to fetch data for {symbol}: {e}")
            return pd.DataFrame()

    def update_market_data(self):
        """Update market data for all monitored pairs"""
        with self.data_lock:
            for symbol in self.config.crypto_pairs:
                try:
                    df = self.get_historical_data(symbol)
                    if not df.empty:
                        # Prepare data for technical analysis
                        df_prepared = self.indicators.prepare_5min_data(df)
                        if df_prepared is not None:
                            # Add MAXIMUM technical indicators (same as training)
                            df_with_indicators = self.indicators.add_maximum_technical_indicators(df_prepared)
                            if df_with_indicators is not None:
                                self.market_data[symbol] = df_with_indicators
                                self.logger.debug(f"📊 Updated data for {symbol}: {len(df_with_indicators)} candles with {len(df_with_indicators.columns)} features")
                            else:
                                self.logger.warning(f"⚠️ Failed to add indicators for {symbol}")
                        else:
                            self.logger.warning(f"⚠️ Failed to prepare data for {symbol}")
                    else:
                        self.logger.warning(f"⚠️ No data received for {symbol}")

                except Exception as e:
                    self.logger.error(f"❌ Error updating data for {symbol}: {e}")

    def prepare_features_for_prediction(self, df: pd.DataFrame, symbol: str) -> np.ndarray:
        """Prepare features for ML model prediction - SAME AS TRAINING"""
        try:
            # Get the last sequence_length rows
            if len(df) < self.config.sequence_length:
                self.logger.warning(f"⚠️ Insufficient data for {symbol}: {len(df)} < {self.config.sequence_length}")
                return None

            # Select feature columns (exclude OHLCV base columns and target columns)
            # Keep only the technical indicator features that were used in training
            exclude_cols = {'Open', 'High', 'Low', 'Close', 'Volume', 'Crypto_Pair'}
            target_cols = {col for col in df.columns if col.startswith('Target_Step_')}
            all_exclude = exclude_cols.union(target_cols)

            feature_cols = [col for col in df.columns if col not in all_exclude]

            self.logger.debug(f"🔧 Using {len(feature_cols)} features for {symbol} prediction")

            # Get the most recent data
            recent_data = df[feature_cols].tail(self.config.sequence_length)

            # Fill any remaining NaN values (same as training)
            recent_data = recent_data.fillna(method='ffill').fillna(method='bfill').fillna(0)

            # Replace infinite values (same as training)
            recent_data = recent_data.replace([np.inf, -np.inf], 0)

            # Scale features using the crypto-specific scaler
            scaled_features = self.scalers[symbol].transform(recent_data)

            # Reshape for LSTM input: (1, sequence_length, n_features)
            features = scaled_features.reshape(1, self.config.sequence_length, -1)

            return features.astype(np.float32)

        except Exception as e:
            self.logger.error(f"❌ Error preparing features for {symbol}: {e}")
            import traceback
            traceback.print_exc()
            return None

    def make_prediction(self, symbol: str) -> Optional[PredictionResult]:
        """Make price prediction using trained model"""
        try:
            if symbol not in self.market_data or symbol not in self.models:
                return None

            df = self.market_data[symbol]
            if df.empty:
                return None

            # Prepare features
            features = self.prepare_features_for_prediction(df, symbol)
            if features is None:
                return None

            # Make prediction
            prediction = self.models[symbol].predict(features, verbose=0)
            predicted_returns = prediction[0]  # Shape: (prediction_horizon,)

            # Convert returns to actual prices
            current_price = float(df['close'].iloc[-1])
            predicted_prices = []

            for i, return_pct in enumerate(predicted_returns):
                if i == 0:
                    predicted_price = current_price * (1 + return_pct)
                else:
                    predicted_price = predicted_prices[-1] * (1 + return_pct)
                predicted_prices.append(predicted_price)

            # Calculate overall predicted gain
            final_predicted_price = predicted_prices[-1]
            predicted_gain = ((final_predicted_price - current_price) / current_price) * 100

            # Calculate confidence (simplified - based on prediction consistency)
            confidence = 1.0 - (np.std(predicted_returns) / (np.abs(np.mean(predicted_returns)) + 1e-8))
            confidence = max(0.0, min(1.0, confidence))

            result = PredictionResult(
                symbol=symbol,
                current_price=current_price,
                predicted_prices=predicted_prices,
                predicted_gain=predicted_gain,
                confidence=confidence,
                timestamp=datetime.now()
            )

            self.logger.debug(f"🔮 {symbol} Prediction: {predicted_gain:.2f}% gain, confidence: {confidence:.2f}")
            return result

        except Exception as e:
            self.logger.error(f"❌ Prediction error for {symbol}: {e}")
            return None

    def get_account_balance(self) -> float:
        """Get current USDT balance"""
        try:
            account = self.client.get_account()
            for balance in account['balances']:
                if balance['asset'] == 'USDT':
                    return float(balance['free'])
            return 0.0
        except Exception as e:
            self.logger.error(f"❌ Error getting balance: {e}")
            return 0.0

    def can_place_trade(self) -> bool:
        """Check if we can place a new trade based on risk management rules"""
        # Check if we have enough balance
        if self.current_balance < self.config.trade_amount:
            return False

        # Check concurrent trades limit
        active_count = len([pos for pos in self.active_positions.values() if pos.status == "ACTIVE"])
        if active_count >= self.config.max_concurrent_trades:
            return False

        # Check if we have enough balance for multiple trades
        if active_count > 0 and self.current_balance < self.config.min_budget_for_multiple_trades:
            return False

        return True

    def calculate_trade_quantity(self, symbol: str, price: float) -> float:
        """Calculate trade quantity based on trade amount"""
        try:
            # Get symbol info for precision
            symbol_info = self.client.get_symbol_info(symbol)
            step_size = None

            for filter_item in symbol_info['filters']:
                if filter_item['filterType'] == 'LOT_SIZE':
                    step_size = float(filter_item['stepSize'])
                    break

            if step_size is None:
                step_size = 0.001  # Default

            # Calculate quantity
            quantity = self.config.trade_amount / price

            # Round to proper precision
            precision = len(str(step_size).split('.')[-1].rstrip('0'))
            quantity = round(quantity, precision)

            return quantity

        except Exception as e:
            self.logger.error(f"❌ Error calculating quantity for {symbol}: {e}")
            return 0.0

    def place_buy_order(self, symbol: str, prediction: PredictionResult) -> Optional[TradePosition]:
        """Place a buy order with OCO sell orders"""
        try:
            current_price = prediction.current_price
            quantity = self.calculate_trade_quantity(symbol, current_price)

            if quantity <= 0:
                self.logger.error(f"❌ Invalid quantity for {symbol}: {quantity}")
                return None

            # Place market buy order
            buy_order = self.client.order_market_buy(
                symbol=symbol,
                quantity=quantity
            )

            if buy_order['status'] != 'FILLED':
                self.logger.error(f"❌ Buy order not filled for {symbol}")
                return None

            # Get actual fill price and quantity
            fill_price = float(buy_order['fills'][0]['price'])
            fill_quantity = float(buy_order['executedQty'])

            # Calculate OCO prices
            take_profit_price = fill_price * (1 + self.config.take_profit / 100)
            stop_loss_price = fill_price * (1 - self.config.stop_loss / 100)
            stop_limit_price = fill_price * (1 - self.config.max_loss / 100)

            # Place OCO sell order
            try:
                oco_order = self.client.create_oco_order(
                    symbol=symbol,
                    side='SELL',
                    quantity=fill_quantity,
                    price=f"{take_profit_price:.8f}",
                    stopPrice=f"{stop_loss_price:.8f}",
                    stopLimitPrice=f"{stop_limit_price:.8f}",
                    stopLimitTimeInForce='GTC'
                )

                oco_order_id = oco_order['orderListId']

            except Exception as e:
                self.logger.error(f"❌ Failed to place OCO order for {symbol}: {e}")
                # Place simple limit sell order as fallback
                try:
                    sell_order = self.client.order_limit_sell(
                        symbol=symbol,
                        quantity=fill_quantity,
                        price=f"{take_profit_price:.8f}"
                    )
                    oco_order_id = sell_order['orderId']
                except Exception as e2:
                    self.logger.error(f"❌ Failed to place fallback sell order: {e2}")
                    oco_order_id = None

            # Create position object
            position = TradePosition(
                symbol=symbol,
                side='BUY',
                quantity=fill_quantity,
                entry_price=fill_price,
                entry_time=datetime.now(),
                order_id=buy_order['orderId'],
                oco_order_id=oco_order_id,
                predicted_gain=prediction.predicted_gain,
                status='ACTIVE'
            )

            # Update trading state
            with self.trading_lock:
                self.active_positions[symbol] = position
                self.current_balance -= (fill_quantity * fill_price)
                self.total_trades += 1

            self.logger.info(f"🟢 BUY {symbol}: {fill_quantity:.6f} @ ${fill_price:.4f}")
            self.logger.info(f"🎯 Take Profit: ${take_profit_price:.4f}, Stop Loss: ${stop_loss_price:.4f}")
            self.logger.info(f"💰 Remaining Balance: ${self.current_balance:.2f}")

            return position

        except BinanceAPIException as e:
            self.logger.error(f"❌ Binance API error placing buy order for {symbol}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Error placing buy order for {symbol}: {e}")
            return None

    def update_positions(self):
        """Update status of active positions"""
        positions_to_remove = []

        with self.trading_lock:
            for symbol, position in self.active_positions.items():
                try:
                    if position.status != 'ACTIVE':
                        continue

                    # Check if OCO order is still active
                    if position.oco_order_id:
                        try:
                            # Check order status
                            orders = self.client.get_open_orders(symbol=symbol)
                            oco_active = any(order['orderId'] == position.oco_order_id for order in orders)

                            if not oco_active:
                                # OCO order executed, check which one
                                order_history = self.client.get_all_orders(symbol=symbol, limit=10)
                                for order in order_history:
                                    if order['orderId'] == position.oco_order_id and order['status'] == 'FILLED':
                                        # Calculate PnL
                                        exit_price = float(order['price'])
                                        pnl = (exit_price - position.entry_price) * position.quantity
                                        pnl_percent = ((exit_price - position.entry_price) / position.entry_price) * 100

                                        position.current_pnl = pnl
                                        position.status = 'CLOSED'

                                        # Update balance and stats
                                        self.current_balance += (position.quantity * exit_price)
                                        self.total_pnl += pnl

                                        if pnl > 0:
                                            self.successful_trades += 1
                                            self.logger.info(f"🟢 PROFIT {symbol}: +${pnl:.2f} ({pnl_percent:.2f}%)")
                                        else:
                                            self.logger.info(f"🔴 LOSS {symbol}: ${pnl:.2f} ({pnl_percent:.2f}%)")

                                        positions_to_remove.append(symbol)
                                        break

                        except Exception as e:
                            self.logger.error(f"❌ Error checking position status for {symbol}: {e}")

                    # Update current PnL for active positions
                    if position.status == 'ACTIVE':
                        try:
                            current_price = float(self.client.get_symbol_ticker(symbol=symbol)['price'])
                            unrealized_pnl = (current_price - position.entry_price) * position.quantity
                            position.current_pnl = unrealized_pnl
                        except:
                            pass

                except Exception as e:
                    self.logger.error(f"❌ Error updating position for {symbol}: {e}")

        # Remove closed positions
        for symbol in positions_to_remove:
            del self.active_positions[symbol]

    def analyze_and_trade(self):
        """Main trading logic - analyze predictions and execute trades"""
        try:
            if not self.can_place_trade():
                return

            # Get predictions for all pairs
            predictions = []
            for symbol in self.config.crypto_pairs:
                if symbol not in self.active_positions:  # Don't trade if already have position
                    prediction = self.make_prediction(symbol)
                    if prediction and prediction.predicted_gain >= self.config.prediction_threshold:
                        predictions.append(prediction)
                        self.predictions_history.append(prediction)

            if not predictions:
                return

            # Sort by predicted gain (highest first)
            predictions.sort(key=lambda x: x.predicted_gain, reverse=True)

            # Log all good predictions
            for pred in predictions:
                self.logger.info(f"🔮 {pred.symbol}: {pred.predicted_gain:.2f}% gain predicted (confidence: {pred.confidence:.2f})")

            # Select best prediction that we can afford
            for prediction in predictions:
                if self.can_place_trade():
                    position = self.place_buy_order(prediction.symbol, prediction)
                    if position:
                        self.logger.info(f"✅ Trade executed for {prediction.symbol}")
                        break
                else:
                    break

        except Exception as e:
            self.logger.error(f"❌ Error in analyze_and_trade: {e}")

    def log_status(self):
        """Log current bot status"""
        active_count = len([pos for pos in self.active_positions.values() if pos.status == "ACTIVE"])
        total_unrealized_pnl = sum(pos.current_pnl for pos in self.active_positions.values() if pos.status == "ACTIVE")

        self.logger.info("=" * 60)
        self.logger.info(f"📊 TRADING BOT STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"💰 Current Balance: ${self.current_balance:.2f}")
        self.logger.info(f"📈 Total PnL: ${self.total_pnl:.2f}")
        self.logger.info(f"📊 Active Positions: {active_count}")
        self.logger.info(f"💹 Unrealized PnL: ${total_unrealized_pnl:.2f}")
        self.logger.info(f"🎯 Total Trades: {self.total_trades}")
        self.logger.info(f"✅ Successful Trades: {self.successful_trades}")

        if self.total_trades > 0:
            success_rate = (self.successful_trades / self.total_trades) * 100
            self.logger.info(f"📊 Success Rate: {success_rate:.1f}%")

        # Log active positions
        for symbol, pos in self.active_positions.items():
            if pos.status == "ACTIVE":
                pnl_percent = (pos.current_pnl / (pos.entry_price * pos.quantity)) * 100
                self.logger.info(f"   {symbol}: ${pos.current_pnl:.2f} ({pnl_percent:.2f}%)")

        self.logger.info("=" * 60)

    def save_state(self):
        """Save current trading state to file"""
        try:
            state = {
                'timestamp': datetime.now().isoformat(),
                'current_balance': self.current_balance,
                'total_pnl': self.total_pnl,
                'total_trades': self.total_trades,
                'successful_trades': self.successful_trades,
                'active_positions': {k: asdict(v) for k, v in self.active_positions.items()},
                'recent_predictions': [asdict(p) for p in self.predictions_history[-50:]]  # Last 50 predictions
            }

            with open(f'trading_state_{datetime.now().strftime("%Y%m%d")}.json', 'w') as f:
                json.dump(state, f, indent=2, default=str)

        except Exception as e:
            self.logger.error(f"❌ Error saving state: {e}")

    def emergency_exit(self):
        """Emergency exit - close all positions"""
        self.logger.warning("🚨 EMERGENCY EXIT - Closing all positions!")

        with self.trading_lock:
            for symbol, position in self.active_positions.items():
                if position.status == "ACTIVE":
                    try:
                        # Cancel existing orders
                        if position.oco_order_id:
                            try:
                                self.client.cancel_order(symbol=symbol, orderId=position.oco_order_id)
                            except:
                                pass

                        # Place market sell order
                        sell_order = self.client.order_market_sell(
                            symbol=symbol,
                            quantity=position.quantity
                        )

                        if sell_order['status'] == 'FILLED':
                            exit_price = float(sell_order['fills'][0]['price'])
                            pnl = (exit_price - position.entry_price) * position.quantity

                            position.current_pnl = pnl
                            position.status = 'EMERGENCY_CLOSED'

                            self.current_balance += (position.quantity * exit_price)
                            self.total_pnl += pnl

                            self.logger.warning(f"🚨 Emergency closed {symbol}: PnL ${pnl:.2f}")

                    except Exception as e:
                        self.logger.error(f"❌ Error in emergency exit for {symbol}: {e}")

    def run_test_mode(self, duration_hours: int = 24):
        """Run bot in test mode for specified duration"""
        self.logger.info(f"🧪 Starting TEST MODE for {duration_hours} hours")
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=duration_hours)

        try:
            while datetime.now() < end_time and self.running:
                # Update market data
                self.update_market_data()

                # Update positions
                self.update_positions()

                # Analyze and trade
                self.analyze_and_trade()

                # Log status every hour
                if datetime.now().minute == 0:
                    self.log_status()

                # Save state
                self.save_state()

                # Wait for next update
                time.sleep(self.config.update_interval)

        except KeyboardInterrupt:
            self.logger.info("🛑 Test mode interrupted by user")
        except Exception as e:
            self.logger.error(f"❌ Error in test mode: {e}")
        finally:
            self.emergency_exit()
            self.log_status()
            self.logger.info("🧪 Test mode completed")

    def run_live_mode(self):
        """Run bot in live trading mode"""
        self.logger.info("🔴 Starting LIVE TRADING MODE")
        self.logger.warning("⚠️ REAL MONEY AT RISK!")

        try:
            while self.running:
                # Update market data
                self.update_market_data()

                # Update positions
                self.update_positions()

                # Analyze and trade
                self.analyze_and_trade()

                # Log status every 30 minutes
                if datetime.now().minute % 30 == 0:
                    self.log_status()

                # Save state every update
                self.save_state()

                # Wait for next update
                time.sleep(self.config.update_interval)

        except KeyboardInterrupt:
            self.logger.info("🛑 Live trading interrupted by user")
        except Exception as e:
            self.logger.error(f"❌ Error in live trading: {e}")
        finally:
            self.emergency_exit()
            self.log_status()
            self.logger.info("🔴 Live trading stopped")

    def stop(self):
        """Stop the trading bot"""
        self.running = False
        self.logger.info("🛑 Trading bot stop signal received")

# ========================================================================================
# CONFIGURATION AND UTILITY FUNCTIONS
# ========================================================================================

def load_config_from_file(config_file: str = "trading_config.ini") -> TradingConfig:
    """Load configuration from INI file"""
    config = configparser.ConfigParser()

    if os.path.exists(config_file):
        config.read(config_file)

        return TradingConfig(
            api_key=config.get('binance', 'api_key', fallback=''),
            api_secret=config.get('binance', 'api_secret', fallback=''),
            testnet=config.getboolean('binance', 'testnet', fallback=True),

            max_budget=config.getfloat('trading', 'max_budget', fallback=30.0),
            trade_amount=config.getfloat('trading', 'trade_amount', fallback=14.0),
            max_concurrent_trades=config.getint('trading', 'max_concurrent_trades', fallback=2),
            min_budget_for_multiple_trades=config.getfloat('trading', 'min_budget_for_multiple_trades', fallback=28.0),

            prediction_threshold=config.getfloat('ml', 'prediction_threshold', fallback=1.9),
            take_profit=config.getfloat('ml', 'take_profit', fallback=1.2),
            stop_loss=config.getfloat('ml', 'stop_loss', fallback=1.5),
            max_loss=config.getfloat('ml', 'max_loss', fallback=1.6),

            models_path=config.get('paths', 'models_path', fallback='/content/drive/MyDrive/crypto_models_separate'),
            scalers_path=config.get('paths', 'scalers_path', fallback='/content/drive/MyDrive/crypto_scalers'),

            crypto_pairs=config.get('trading', 'crypto_pairs', fallback='BTCUSDT,ETHUSDT,ADAUSDT,BNBUSDT,SOLUSDT').split(',')
        )
    else:
        # Create default config file
        create_default_config(config_file)
        return TradingConfig()

def create_default_config(config_file: str = "trading_config.ini"):
    """Create default configuration file"""
    config = configparser.ConfigParser()

    config['binance'] = {
        'api_key': 'GWf3NmhOQi9q2KIhDLcugL1smupKLfwSzlSmY3TwexJqNf4ehDpXs4qy2eeZYviW',
        'api_secret': 'gJCCFfWoUCzDFfs9b6JCH6Q6yMg3vKSqxK5k5EnQ4rwHJXT6jPBvnyVaaJky14pC',
        'testnet': 'True'
    }

    config['trading'] = {
        'max_budget': '30.0',
        'trade_amount': '14.0',
        'max_concurrent_trades': '2',
        'min_budget_for_multiple_trades': '28.0',
        'crypto_pairs': 'BTCUSDT,ETHUSDT,ADAUSDT,BNBUSDT,SOLUSDT'
    }

    config['ml'] = {
        'prediction_threshold': '1.9',
        'take_profit': '1.2',
        'stop_loss': '1.5',
        'max_loss': '1.6'
    }

    config['paths'] = {
        'models_path': '/content/drive/MyDrive/crypto_models_separate',
        'scalers_path': '/content/drive/MyDrive/crypto_scalers'
    }

    with open(config_file, 'w') as f:
        config.write(f)

    print(f"✅ Created default config file: {config_file}")
    print("⚠️ Please edit the config file with your Binance API credentials!")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\n🛑 Shutdown signal received. Stopping bot...")
    global bot
    if 'bot' in globals():
        bot.stop()
    sys.exit(0)

# ========================================================================================
# MAIN EXECUTION
# ========================================================================================

def main():
    """Main execution function"""
    print("🚀 Advanced Crypto Trading Bot with ML Predictions")
    print("=" * 60)

    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Load configuration
    try:
        config = load_config_from_file()
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return

    # Validate API credentials
    if not config.api_key or config.api_key == 'YOUR_BINANCE_API_KEY':
        print("❌ Please set your Binance API credentials in trading_config.ini")
        return

    # Initialize bot
    try:
        global bot
        bot = CryptoTradingBot(config)
        print("✅ Trading bot initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing bot: {e}")
        return

    # Choose mode
    print("\n📋 Select trading mode:")
    print("1. Test Mode (24 hours)")
    print("2. Test Mode (Custom duration)")
    print("3. Live Trading Mode")
    print("4. Exit")

    try:
        choice = input("\nEnter your choice (1-4): ").strip()

        if choice == '1':
            bot.run_test_mode(24)
        elif choice == '2':
            hours = int(input("Enter duration in hours: "))
            bot.run_test_mode(hours)
        elif choice == '3':
            confirm = input("⚠️ Are you sure you want to start LIVE trading? (yes/no): ").strip().lower()
            if confirm == 'yes':
                bot.run_live_mode()
            else:
                print("Live trading cancelled.")
        elif choice == '4':
            print("Exiting...")
        else:
            print("Invalid choice.")

    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if 'bot' in globals():
            bot.stop()

if __name__ == "__main__":
    main()

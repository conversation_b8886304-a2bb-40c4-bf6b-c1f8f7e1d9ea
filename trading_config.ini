[binance]
# Your Binance API credentials
api_key = XCiqbiDeX4XxhdtAe3qNFj0VfYFa2xSzub98qZWFbrJnWxHUP8WSXju7nJC8Y3Sf
api_secret = 37wzmTifWfdPKc4jK6DrNmAphSKffT3cFBvJhVCZxyM4nsRfyERiBi9x1To9ao4c
# Set to False for live trading, True for testnet
testnet = True

[trading]
# Maximum budget to use for trading (USDT)
max_budget = 30.0
# Amount per trade (USDT)
trade_amount = 14.0
# Maximum number of concurrent trades
max_concurrent_trades = 2
# Minimum budget required for multiple trades
min_budget_for_multiple_trades = 28.0
# Crypto pairs to monitor (comma-separated)
crypto_pairs = XRPUSDT

[ml]
# Minimum predicted gain % to trigger a buy
prediction_threshold = 1.9
# Take profit percentage
take_profit = 1.2
# Stop loss percentage
stop_loss = 1.5
# Maximum loss percentage (emergency exit)
max_loss = 1.6

[paths]
# Path to trained models
models_path = models
# Path to scalers
scalers_path = scalers

[advanced]
# Trading fee percentage
trading_fee = 0.1
# Prediction horizon (steps ahead)
prediction_horizon = 10
# Sequence length for model input
sequence_length = 60
# Data update interval in seconds (300 = 5 minutes)
update_interval = 300
# Logging level (DEBUG, INFO, WARNING, ERROR)
log_level = INFO

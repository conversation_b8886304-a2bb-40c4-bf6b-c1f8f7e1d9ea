# =========================
# ENHANCED HIGH-PERFORMANCE CONFIGURATION - MODIFIED FOR SEPARATE CRYPTO MODELS
# =========================
"""
Maximum Technical Indicators Mode for 10GB RAM Colab Environment
Now with SEPARATE neural network per crypto pair
Optimized chunk_size and all 300+ comprehensive technical indicators preserved
"""

# Enhanced performance settings with separate models per crypto
ULTRA_ENHANCED_CONFIG = {
    "chunk_size": 20000,         # ✅ fine-grained, quick feedback
    "batch_size": 512,           # 🔧 reduced for Bi-LSTM(128) and 300+ indicators
    "prefetch_chunks": 4,        # 🔁 slightly increased to compensate for smaller chunk size
    "mixed_precision": True,     # ✅ must-have with large model
    "aggressive_caching": True,  # ✅ keeps small chunks fast
    "max_indicators": True,      # ✅ full feature set = good for LSTM
    "prediction_horizon": 10,    # 🆕 NEW: Configurable steps ahead (1-50)
    "sequence_length": 60,       # 📊 Look-back window
    "horizon_type": "regression", # 🎯 "regression" for price prediction or "classification" for signals
    "separate_models": True      # 🆕 NEW: Create separate model per crypto pair
}

print(f"🚀 MAXIMUM INDICATORS MODE WITH SEPARATE CRYPTO MODELS ACTIVATED")
print(f"📊 Configuration: {ULTRA_ENHANCED_CONFIG}")
print(f"🎯 Target: {ULTRA_ENHANCED_CONFIG['prediction_horizon']} steps ahead with 300+ indicators per crypto pair")

# =========================
# 1. Install & Imports (MODIFIED FOR SEPARATE MODELS)
# =========================
#!pip install pandas numpy scikit-learn tensorflow joblib matplotlib seaborn yfinance pydrive scipy statsmodels plotly python-binance ccxt websocket-client backtesting pyarrow talib

from google.colab import drive
drive.mount('/content/drive')

import pandas as pd
import numpy as np
import os, glob, gc, json, warnings
from datetime import datetime
warnings.filterwarnings('ignore')

from sklearn.preprocessing import StandardScaler
import tensorflow as tf
import joblib  # For saving scalers

# =========================
# 2. OPTIMIZED TensorFlow Configuration (UNCHANGED)
# =========================
print("🔧 Configuring TensorFlow for maximum indicator processing...")

tf.config.run_functions_eagerly(False)

gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(f"GPU configuration error: {e}")

if ULTRA_ENHANCED_CONFIG["mixed_precision"]:
    policy = tf.keras.mixed_precision.Policy('mixed_float16')
    tf.keras.mixed_precision.set_global_policy(policy)
    print("✅ Mixed precision enabled for 300+ indicators")

try:
    strategy = tf.distribute.MirroredStrategy()
    print(f'🚀 Using {strategy.num_replicas_in_sync} GPU(s) for maximum indicators')
except:
    strategy = tf.distribute.get_strategy()

from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization, Bidirectional, SpatialDropout1D
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.utils import Sequence

# =========================
# 3. MAXIMUM TECHNICAL INDICATORS ENGINE (UNCHANGED - ALL 300+ PRESERVED)
# =========================
# [Your entire MaximumTechnicalAnalyzer class remains exactly the same]
# All your 300+ indicators, harmonic patterns, Elliott waves, etc. are preserved

class MaximumTechnicalAnalyzer:
    def __init__(self, sequence_length=60):
        self.sequence_length = sequence_length
        self.scaler = StandardScaler()

    def create_multi_horizon_target(self, df, horizon=10, target_type="regression"):
        """
        Create multi-step ahead targets

        Args:
            df: DataFrame with OHLCV data
            horizon: Number of steps to predict ahead
            target_type: "regression" for price prediction, "classification" for signals
        """
        df = df.copy()

        if target_type == "regression":
            # Predict future close prices (normalized returns)
            targets = []
            for i in range(len(df)):
                if i >= len(df) - horizon:
                    # Pad with zeros for last samples
                    targets.append([0.0] * horizon)
                    continue

                current_price = df['Close'].iloc[i]
                future_prices = []

                for step in range(1, horizon + 1):
                    if i + step < len(df):
                        future_price = df['Close'].iloc[i + step]
                        # Normalized return
                        normalized_return = (future_price - current_price) / current_price
                        future_prices.append(normalized_return)
                    else:
                        future_prices.append(0.0)

                targets.append(future_prices)

            # Create target columns
            for step in range(horizon):
                df[f'Target_Step_{step+1}'] = [target[step] for target in targets]

        else:  # classification
            # Predict future signal directions
            profit_threshold = 0.015
            loss_threshold = -0.015

            targets = []
            for i in range(len(df)):
                if i >= len(df) - horizon:
                    targets.append([0] * horizon)
                    continue

                current_price = df['Close'].iloc[i]
                future_signals = []

                for step in range(1, horizon + 1):
                    if i + step < len(df):
                        future_price = df['Close'].iloc[i + step]
                        return_pct = (future_price - current_price) / current_price

                        if return_pct >= profit_threshold:
                            signal = 1  # Buy signal
                        elif return_pct <= loss_threshold:
                            signal = 0  # Sell signal
                        else:
                            signal = 0.5  # Hold signal

                        future_signals.append(signal)
                    else:
                        future_signals.append(0)

                targets.append(future_signals)

            # Create target columns
            for step in range(horizon):
                df[f'Target_Step_{step+1}'] = [target[step] for target in targets]

        return df
    # Basic calculation functions
    def sma(self, data, period): return data.rolling(window=period).mean()
    def ema(self, data, period): return data.ewm(span=period).mean()
    def wma(self, data, period):
        weights = np.arange(1, period + 1)
        return data.rolling(period).apply(lambda x: np.dot(x, weights) / weights.sum(), raw=True)
    def tema(self, data, period):
        ema1 = self.ema(data, period)
        ema2 = self.ema(ema1, period)
        ema3 = self.ema(ema2, period)
        return 3 * (ema1 - ema2) + ema3
    def dema(self, data, period):
        ema1 = self.ema(data, period)
        ema2 = self.ema(ema1, period)
        return 2 * ema1 - ema2
    def hma(self, data, period):
        # Hull Moving Average[8]
        wma1 = self.wma(data, period // 2) * 2
        wma2 = self.wma(data, period)
        raw_hma = wma1 - wma2
        return self.wma(raw_hma, int(np.sqrt(period)))
    def kama(self, data, period=10):
        change = abs(data - data.shift(period))
        volatility = abs(data - data.shift(1)).rolling(period).sum()
        er = change / volatility
        sc = ((er * (2.0 / (2 + 1) - 2.0 / (30 + 1)) + 2.0 / (30 + 1)) ** 2.0)
        kama = [data.iloc[0]]
        for i in range(1, len(data)):
            kama.append(kama[-1] + sc.iloc[i] * (data.iloc[i] - kama[-1]))
        return pd.Series(kama, index=data.index)

    def rsi(self, data, period=14):
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def stochastic(self, high, low, close, k_period=14, d_period=3):
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)
        d_percent = k_percent.rolling(window=d_period).mean()
        return k_percent, d_percent

    def williams_r(self, high, low, close, period=14):
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        return -100 * (highest_high - close) / (highest_high - lowest_low)

    def cci(self, high, low, close, period=20):
        tp = (high + low + close) / 3
        ma = tp.rolling(period).mean()
        md = tp.rolling(period).apply(lambda x: np.mean(np.abs(x - x.mean())), raw=True)
        return (tp - ma) / (0.015 * md)

    def ultimate_oscillator(self, high, low, close, p1=7, p2=14, p3=28):
        tr = self.true_range(high, low, close)
        bp = close - np.minimum(low, close.shift(1))
        avg7 = bp.rolling(p1).sum() / tr.rolling(p1).sum()
        avg14 = bp.rolling(p2).sum() / tr.rolling(p2).sum()
        avg28 = bp.rolling(p3).sum() / tr.rolling(p3).sum()
        return 100 * (4 * avg7 + 2 * avg14 + avg28) / 7

    def macd(self, data, fast=12, slow=26, signal=9):
        ema_fast = self.ema(data, fast)
        ema_slow = self.ema(data, slow)
        macd = ema_fast - ema_slow
        signal_line = self.ema(macd, signal)
        histogram = macd - signal_line
        return macd, signal_line, histogram

    def bollinger_bands(self, data, period=20, std_dev=2):
        sma = self.sma(data, period)
        std = data.rolling(window=period).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower

    def keltner_channels(self, high, low, close, period=20, multiplier=2):
        # Keltner Channels[12]
        ema = self.ema(close, period)
        atr = self.atr(high, low, close, period)
        upper = ema + (multiplier * atr)
        lower = ema - (multiplier * atr)
        return upper, ema, lower

    def true_range(self, high, low, close):
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        return pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

    def atr(self, high, low, close, period=14):
        return self.true_range(high, low, close).rolling(window=period).mean()

    def adx(self, high, low, close, period=14):
        tr = self.true_range(high, low, close)
        plus_dm = high.diff()
        minus_dm = -low.diff()
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0
        plus_di = 100 * (plus_dm.rolling(period).mean() / tr.rolling(period).mean())
        minus_di = 100 * (minus_dm.rolling(period).mean() / tr.rolling(period).mean())
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(period).mean()
        return adx, plus_di, minus_di

    def aroon(self, high, low, period=25):
        aroon_up = high.rolling(period + 1).apply(lambda x: x.argmax(), raw=True) / period * 100
        aroon_down = low.rolling(period + 1).apply(lambda x: x.argmin(), raw=True) / period * 100
        return aroon_up, aroon_down

    def obv(self, close, volume):
        obv = [0]
        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.append(obv[-1] + volume.iloc[i])
            elif close.iloc[i] < close.iloc[i-1]:
                obv.append(obv[-1] - volume.iloc[i])
            else:
                obv.append(obv[-1])
        return pd.Series(obv, index=close.index)

    def vwap(self, high, low, close, volume):
        typical_price = (high + low + close) / 3
        return (typical_price * volume).cumsum() / volume.cumsum()

    def mfi(self, high, low, close, volume, period=14):
        # Money Flow Index[12]
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0).rolling(period).sum()
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0).rolling(period).sum()
        return 100 - (100 / (1 + positive_flow / negative_flow))

    def chaikin_money_flow(self, high, low, close, volume, period=20):
        # Chaikin Money Flow[12]
        mfv = ((close - low) - (high - close)) / (high - low) * volume
        return mfv.rolling(period).sum() / volume.rolling(period).sum()

    def chaikin_oscillator(self, high, low, close, volume):
        # Chaikin Oscillator[12]
        ad_line = ((close - low) - (high - close)) / (high - low) * volume
        ad_line = ad_line.fillna(0).cumsum()
        return self.ema(ad_line, 3) - self.ema(ad_line, 10)

    def awesome_oscillator(self, high, low):
        # Bill Williams Awesome Oscillator[12]
        median_price = (high + low) / 2
        ao = self.sma(median_price, 5) - self.sma(median_price, 34)
        return ao

    def accelerator_decelerator(self, high, low, close):
        # Bill Williams Accelerator/Decelerator[12]
        ao = self.awesome_oscillator(high, low)
        ac = ao - self.sma(ao, 5)
        return ac

    def market_facilitation_index(self, high, low, volume):
        # Bill Williams Market Facilitation Index[12]
        return (high - low) / volume

    def wave_trend_oscillator(self, high, low, close, period1=10, period2=21):
        # Wave Trend Oscillator[12]
        ap = (high + low + close) / 3
        esa = self.ema(ap, period1)
        d = self.ema(abs(ap - esa), period1)
        ci = (ap - esa) / (0.015 * d)
        tci = self.ema(ci, period2)
        return tci

    def dinapoli_macd(self, data, fast=8, slow=17, signal=9):
        # DiNapoli MACD[12]
        return self.macd(data, fast, slow, signal)

    def ichimoku_cloud(self, high, low, close):
        # Complete Ichimoku Cloud System[8][9]
        # Tenkan-sen (Conversion Line)
        tenkan = (high.rolling(9).max() + low.rolling(9).min()) / 2

        # Kijun-sen (Base Line)
        kijun = (high.rolling(26).max() + low.rolling(26).min()) / 2

        # Senkou Span A (Leading Span A)
        senkou_a = ((tenkan + kijun) / 2).shift(26)

        # Senkou Span B (Leading Span B)
        senkou_b = ((high.rolling(52).max() + low.rolling(52).min()) / 2).shift(26)

        # Chikou Span (Lagging Span)
        chikou = close.shift(-26)

        return tenkan, kijun, senkou_a, senkou_b, chikou

    def heikin_ashi(self, open_price, high, low, close):
        # Heikin Ashi Candlesticks[8][9]
        ha_close = (open_price + high + low + close) / 4
        ha_open = pd.Series(index=close.index, dtype='float64')
        ha_open.iloc[0] = (open_price.iloc[0] + close.iloc[0]) / 2

        for i in range(1, len(close)):
            ha_open.iloc[i] = (ha_open.iloc[i-1] + ha_close.iloc[i-1]) / 2

        ha_high = pd.concat([high, ha_open, ha_close], axis=1).max(axis=1)
        ha_low = pd.concat([low, ha_open, ha_close], axis=1).min(axis=1)

        return ha_open, ha_high, ha_low, ha_close

    def chande_momentum_oscillator(self, data, period=14):
        momentum = data.diff()
        sum_up = momentum.where(momentum > 0, 0).rolling(period).sum()
        sum_down = momentum.where(momentum < 0, 0).abs().rolling(period).sum()
        return 100 * (sum_up - sum_down) / (sum_up + sum_down)

    def volume_rate_of_change(self, volume, period=14):
        # Volume Rate of Change[12]
        return (volume / volume.shift(period) - 1) * 100

    def volume_oscillator(self, volume, short_period=5, long_period=10):
        short_ma = self.sma(volume, short_period)
        long_ma = self.sma(volume, long_period)
        return (short_ma - long_ma) / long_ma * 100

    def safe_divide(self, a, b):
        return np.where(b != 0, a / b, 0)

    def optimize_dtypes(self, df):
        """Optimize data types to reduce memory usage"""
        for col, dt in df.dtypes.items():
            if dt.kind in "iu":
                df[col] = pd.to_numeric(df[col], downcast="unsigned" if (df[col] >= 0).all() else "integer")
            elif dt.kind == "f":
                df[col] = pd.to_numeric(df[col], downcast="float")
            elif dt == "object":
                nunique = df[col].nunique(dropna=False)
                if nunique / len(df) < 0.5:
                    df[col] = df[col].astype("category")
        return df

    def detect_harmonic_patterns(self, high, low, close):
        # Harmonic Pattern Detection[10]
        patterns = {}

        # Gartley Pattern ratios
        patterns['GARTLEY_XA'] = (close / close.shift(5) - 1) * 100  # XA leg
        patterns['GARTLEY_AB'] = (close / close.shift(3) - 1) * 100  # AB leg
        patterns['GARTLEY_BC'] = (close / close.shift(2) - 1) * 100  # BC leg

        # Butterfly Pattern ratios
        patterns['BUTTERFLY_XA'] = (close / close.shift(7) - 1) * 100
        patterns['BUTTERFLY_AB'] = (close / close.shift(4) - 1) * 100

        # Bat Pattern ratios
        patterns['BAT_XA'] = (close / close.shift(6) - 1) * 100
        patterns['BAT_AB'] = (close / close.shift(3) - 1) * 100

        # Crab Pattern ratios
        patterns['CRAB_XA'] = (close / close.shift(8) - 1) * 100
        patterns['CRAB_AB'] = (close / close.shift(5) - 1) * 100

        return patterns

    def elliott_wave_ratios(self, close):
        # Elliott Wave Theory ratios[10]
        ratios = {}

        # Wave relationships
        ratios['WAVE_RATIO_618'] = (close / close.shift(5) - 1) / 0.618
        ratios['WAVE_RATIO_382'] = (close / close.shift(3) - 1) / 0.382
        ratios['WAVE_RATIO_236'] = (close / close.shift(2) - 1) / 0.236
        ratios['WAVE_RATIO_1618'] = (close / close.shift(8) - 1) / 1.618

        # Impulse wave characteristics
        ratios['IMPULSE_WAVE_1'] = close - close.shift(5)
        ratios['IMPULSE_WAVE_3'] = close - close.shift(3)
        ratios['IMPULSE_WAVE_5'] = close - close.shift(1)

        # Corrective wave characteristics
        ratios['CORRECTIVE_A'] = close - close.shift(4)
        ratios['CORRECTIVE_B'] = close - close.shift(2)
        ratios['CORRECTIVE_C'] = close - close.shift(1)

        return ratios

    def advanced_candlestick_patterns(self, open_price, high, low, close):
        # Enhanced Candlestick Pattern Recognition[8]
        patterns = {}

        # Basic patterns (already exist, expanding)
        body = abs(close - open_price)
        range_hl = high - low
        upper_shadow = high - np.maximum(open_price, close)
        lower_shadow = np.minimum(open_price, close) - low

        # Advanced patterns
        patterns['ABANDONED_BABY'] = (
            (abs(high.shift(1) - low) > 0) &
            (abs(high.shift(1) - low.shift(-1)) > 0) &
            (body.shift(1) < range_hl.shift(1) * 0.1)
        ).astype(int)

        patterns['BELT_HOLD_BULL'] = (
            (close > open_price) &
            (lower_shadow < body * 0.1) &
            (body > range_hl * 0.7)
        ).astype(int)

        patterns['BELT_HOLD_BEAR'] = (
            (close < open_price) &
            (upper_shadow < body * 0.1) &
            (body > range_hl * 0.7)
        ).astype(int)

        patterns['BREAKAWAY'] = (
            (close.shift(4) < open_price.shift(4)) &  # First bearish
            (low.shift(3) > high.shift(4)) &  # Gap down
            (close.shift(3) < open_price.shift(3)) &  # Second bearish
            (close.shift(2) < open_price.shift(2)) &  # Third bearish
            (close.shift(1) < open_price.shift(1)) &  # Fourth bearish
            (close > open_price) &  # Fifth bullish
            (close > open_price.shift(4))  # Close above first candle
        ).astype(int)

        patterns['CONCEALING_BABY_SWALLOW'] = (
            (close.shift(3) < open_price.shift(3)) &  # First bearish
            (close.shift(2) < open_price.shift(2)) &  # Second bearish
            (close.shift(1) < open_price.shift(1)) &  # Third bearish
            (close < open_price) &  # Fourth bearish
            (close.shift(1) > close.shift(2)) &  # Third closes higher
            (close > close.shift(1))  # Fourth closes higher
        ).astype(int)

        patterns['COUNTERATTACK'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close > open_price) &  # Second bullish
            (abs(close - close.shift(1)) < range_hl.shift(1) * 0.1)  # Same close
        ).astype(int)

        patterns['DRAGONFLY_DOJI'] = (
            (abs(close - open_price) < range_hl * 0.1) &
            (upper_shadow < range_hl * 0.1) &
            (lower_shadow > range_hl * 0.6)
        ).astype(int)

        patterns['GRAVESTONE_DOJI'] = (
            (abs(close - open_price) < range_hl * 0.1) &
            (lower_shadow < range_hl * 0.1) &
            (upper_shadow > range_hl * 0.6)
        ).astype(int)

        patterns['HANGING_MAN'] = (
            (lower_shadow > body * 2) &
            (upper_shadow < body * 0.3) &
            (close < open_price) &
            (close.shift(1) > open_price.shift(1))  # Previous bullish trend
        ).astype(int)

        patterns['INVERTED_HAMMER'] = (
            (upper_shadow > body * 2) &
            (lower_shadow < body * 0.3) &
            (close.shift(1) < open_price.shift(1))  # Previous bearish trend
        ).astype(int)

        patterns['LONG_LEGGED_DOJI'] = (
            (abs(close - open_price) < range_hl * 0.1) &
            (upper_shadow > range_hl * 0.3) &
            (lower_shadow > range_hl * 0.3)
        ).astype(int)

        patterns['MARUBOZU_WHITE'] = (
            (close > open_price) &
            (upper_shadow < range_hl * 0.01) &
            (lower_shadow < range_hl * 0.01)
        ).astype(int)

        patterns['MARUBOZU_BLACK'] = (
            (close < open_price) &
            (upper_shadow < range_hl * 0.01) &
            (lower_shadow < range_hl * 0.01)
        ).astype(int)

        patterns['MATCHING_LOW'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close < open_price) &  # Second bearish
            (abs(low - low.shift(1)) < range_hl * 0.05)  # Same lows
        ).astype(int)

        patterns['ON_NECK'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close > open_price) &  # Second bullish
            (close <= close.shift(1) + range_hl.shift(1) * 0.05)  # Closes at previous close
        ).astype(int)

        patterns['PIERCING_LINE'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close > open_price) &  # Second bullish
            (open_price < low.shift(1)) &  # Opens below previous low
            (close > (open_price.shift(1) + close.shift(1)) / 2)  # Closes above midpoint
        ).astype(int)

        patterns['RICKSHAW_MAN'] = (
            (abs(close - open_price) < range_hl * 0.1) &
            (upper_shadow > range_hl * 0.25) &
            (lower_shadow > range_hl * 0.25)
        ).astype(int)

        patterns['RISING_THREE_METHODS'] = (
            (close.shift(4) > open_price.shift(4)) &  # First bullish
            (close.shift(3) < open_price.shift(3)) &  # Second bearish
            (close.shift(2) < open_price.shift(2)) &  # Third bearish
            (close.shift(1) < open_price.shift(1)) &  # Fourth bearish
            (close > open_price) &  # Fifth bullish
            (close > close.shift(4)) &  # Closes above first
            (high.shift(3) < close.shift(4)) &  # Contained within first
            (high.shift(2) < close.shift(4)) &
            (high.shift(1) < close.shift(4))
        ).astype(int)

        patterns['SEPARATING_LINES'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close > open_price) &  # Second bullish
            (abs(open_price - open_price.shift(1)) < range_hl * 0.05)  # Same opens
        ).astype(int)

        patterns['SHOOTING_STAR'] = (
            (upper_shadow > body * 2) &
            (lower_shadow < body * 0.3) &
            (close < open_price) &
            (close.shift(1) > open_price.shift(1))  # Previous bullish trend
        ).astype(int)

        patterns['SPINNING_TOP'] = (
            (body < range_hl * 0.3) &
            (upper_shadow > body) &
            (lower_shadow > body)
        ).astype(int)

        patterns['STALLED_PATTERN'] = (
            (close.shift(2) > open_price.shift(2)) &  # First bullish
            (close.shift(1) > open_price.shift(1)) &  # Second bullish
            (close > open_price) &  # Third bullish
            (body.shift(1) > body.shift(2)) &  # Second larger than first
            (body < body.shift(1)) &  # Third smaller than second
            (upper_shadow > body * 0.5)  # Third has upper shadow
        ).astype(int)

        patterns['STICK_SANDWICH'] = (
            (close.shift(2) < open_price.shift(2)) &  # First bearish
            (close.shift(1) > open_price.shift(1)) &  # Second bullish
            (close < open_price) &  # Third bearish
            (abs(close - close.shift(2)) < range_hl * 0.05)  # Same closes
        ).astype(int)

        patterns['TAKURI'] = (
            (lower_shadow > body * 3) &
            (upper_shadow < body * 0.1) &
            (body < range_hl * 0.2)
        ).astype(int)

        patterns['TASUKI_GAP'] = (
            (close.shift(2) > open_price.shift(2)) &  # First bullish
            (close.shift(1) > open_price.shift(1)) &  # Second bullish
            (open_price.shift(1) > close.shift(2)) &  # Gap up
            (close < open_price) &  # Third bearish
            (close < close.shift(1)) &  # Closes below second
            (close > close.shift(2))  # But above first
        ).astype(int)

        patterns['THREE_INSIDE_UP'] = (
            (close.shift(2) < open_price.shift(2)) &  # First bearish
            (close.shift(1) > open_price.shift(1)) &  # Second bullish (inside first)
            (open_price.shift(1) > close.shift(2)) &  # Opens above first close
            (close.shift(1) < open_price.shift(2)) &  # Closes below first open
            (close > open_price) &  # Third bullish
            (close > close.shift(1))  # Closes above second
        ).astype(int)

        patterns['THREE_OUTSIDE_UP'] = (
            (close.shift(2) < open_price.shift(2)) &  # First bearish
            (close.shift(1) > open_price.shift(1)) &  # Second bullish (engulfs first)
            (open_price.shift(1) < close.shift(2)) &  # Opens below first close
            (close.shift(1) > open_price.shift(2)) &  # Closes above first open
            (close > open_price) &  # Third bullish
            (close > close.shift(1))  # Closes above second
        ).astype(int)

        patterns['THRUSTING'] = (
            (close.shift(1) < open_price.shift(1)) &  # First bearish
            (close > open_price) &  # Second bullish
            (open_price < close.shift(1)) &  # Opens below first close
            (close < (open_price.shift(1) + close.shift(1)) / 2)  # Closes below midpoint
        ).astype(int)

        patterns['TRISTAR'] = (
            (abs(close.shift(2) - open_price.shift(2)) < range_hl.shift(2) * 0.1) &  # First doji
            (abs(close.shift(1) - open_price.shift(1)) < range_hl.shift(1) * 0.1) &  # Second doji
            (abs(close - open_price) < range_hl * 0.1) &  # Third doji
            (high.shift(1) > high.shift(2)) &  # Second higher than first
            (high.shift(1) > high) &  # Second higher than third
            (low.shift(1) < low.shift(2)) &  # Second lower than first
            (low.shift(1) < low)  # Second lower than third
        ).astype(int)

        patterns['UNIQUE_THREE_RIVER_BOTTOM'] = (
            (close.shift(2) < open_price.shift(2)) &  # First bearish
            (close.shift(1) < open_price.shift(1)) &  # Second bearish
            (abs(close - open_price) < range_hl * 0.1) &  # Third doji
            (low.shift(1) < low.shift(2)) &  # Second makes new low
            (low > low.shift(1))  # Third above second low
        ).astype(int)

        patterns['UPSIDE_GAP_TWO_CROWS'] = (
            (close.shift(2) > open_price.shift(2)) &  # First bullish
            (close.shift(1) < open_price.shift(1)) &  # Second bearish
            (close < open_price) &  # Third bearish
            (open_price.shift(1) > close.shift(2)) &  # Gap up
            (close < close.shift(1)) &  # Third closes below second
            (open_price > open_price.shift(1))  # Third opens above second
        ).astype(int)

        return patterns

    # COMPREHENSIVE INDICATOR COLLECTION (300+ FEATURES)
    def add_maximum_technical_indicators(self, df):
        """Add 300+ comprehensive technical indicators and patterns"""
        df = df.copy()
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        if not all(col in df.columns for col in required_cols):
            return None

        for col in required_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        high, low, close, open_price, volume = df['High'], df['Low'], df['Close'], df['Open'], df['Volume']

        print("🔄 Adding MAXIMUM technical indicators (300+)...")

        # 1. MOVING AVERAGES (60 features) - Enhanced from previous
        ma_periods = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 25, 26, 30, 34, 40, 50, 55, 60, 89, 100, 120, 144, 200]
        for period in ma_periods:
            if period <= len(df):
                df[f'SMA_{period}'] = self.sma(close, period)
                df[f'EMA_{period}'] = self.ema(close, period)

        # Advanced moving averages
        adv_ma_periods = [8, 10, 13, 20, 21, 34, 55, 89]
        for period in adv_ma_periods:
            if period <= len(df):
                df[f'WMA_{period}'] = self.wma(close, period)
                df[f'TEMA_{period}'] = self.tema(close, period)
                df[f'DEMA_{period}'] = self.dema(close, period)
                df[f'HMA_{period}'] = self.hma(close, period)

        # Kaufman Adaptive MA
        for period in [10, 14, 20, 30]:
            df[f'KAMA_{period}'] = self.kama(close, period)

        # 2. MOMENTUM OSCILLATORS (80 features) - Massively enhanced
        rsi_periods = [2, 6, 7, 9, 11, 14, 17, 21, 25, 30]
        for period in rsi_periods:
            df[f'RSI_{period}'] = self.rsi(close, period)
            df[f'RSI_VOLUME_{period}'] = self.rsi(volume, period)
            df[f'RSI_HIGH_{period}'] = self.rsi(high, period)
            df[f'RSI_LOW_{period}'] = self.rsi(low, period)

        # Stochastic variants
        stoch_configs = [(5,3), (8,3), (14,3), (21,5), (25,5), (34,8)]
        for k, d in stoch_configs:
            k_val, d_val = self.stochastic(high, low, close, k, d)
            df[f'STOCH_K_{k}_{d}'] = k_val
            df[f'STOCH_D_{k}_{d}'] = d_val
            df[f'STOCH_RSI_{k}'] = self.stochastic(df[f'RSI_{k}'], df[f'RSI_{k}'], df[f'RSI_{k}'], 14)[0] if f'RSI_{k}' in df.columns else 0

        # Williams %R variants
        willr_periods = [6, 10, 14, 21, 28, 34]
        for period in willr_periods:
            df[f'WILLR_{period}'] = self.williams_r(high, low, close, period)

        # CCI variants
        cci_periods = [14, 17, 20, 25, 30, 34, 40]
        for period in cci_periods:
            df[f'CCI_{period}'] = self.cci(high, low, close, period)

        # Advanced oscillators
        df['UO'] = self.ultimate_oscillator(high, low, close)
        df['CMO_14'] = self.chande_momentum_oscillator(close, 14)
        df['CMO_20'] = self.chande_momentum_oscillator(close, 20)

        # Bill Williams Indicators[12]
        df['AWESOME_OSC'] = self.awesome_oscillator(high, low)
        df['AC_OSC'] = self.accelerator_decelerator(high, low, close)
        df['MFI_BW'] = self.market_facilitation_index(high, low, volume)

        # Wave Trend Oscillator[12]
        df['WTO_10_21'] = self.wave_trend_oscillator(high, low, close, 10, 21)
        df['WTO_14_28'] = self.wave_trend_oscillator(high, low, close, 14, 28)

        # 3. TREND INDICATORS (35 features) - Enhanced
        adx_periods = [14, 17, 20, 25, 28]
        for period in adx_periods:
            adx_val, plus_di, minus_di = self.adx(high, low, close, period)
            df[f'ADX_{period}'] = adx_val
            df[f'PLUS_DI_{period}'] = plus_di
            df[f'MINUS_DI_{period}'] = minus_di
            df[f'DX_{period}'] = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)

        # Aroon variants
        aroon_periods = [14, 20, 25, 28, 30]
        for period in aroon_periods:
            aroon_up, aroon_down = self.aroon(high, low, period)
            df[f'AROON_UP_{period}'] = aroon_up
            df[f'AROON_DOWN_{period}'] = aroon_down
            df[f'AROON_OSC_{period}'] = aroon_up - aroon_down

        # Ichimoku Cloud System[8][9]
        tenkan, kijun, senkou_a, senkou_b, chikou = self.ichimoku_cloud(high, low, close)
        df['ICHIMOKU_TENKAN'] = tenkan
        df['ICHIMOKU_KIJUN'] = kijun
        df['ICHIMOKU_SENKOU_A'] = senkou_a
        df['ICHIMOKU_SENKOU_B'] = senkou_b
        df['ICHIMOKU_CHIKOU'] = chikou

        # Cloud analysis
        df['ICHIMOKU_CLOUD_GREEN'] = (senkou_a > senkou_b).astype(int)
        df['ICHIMOKU_ABOVE_CLOUD'] = (close > np.maximum(senkou_a, senkou_b)).astype(int)
        df['ICHIMOKU_BELOW_CLOUD'] = (close < np.minimum(senkou_a, senkou_b)).astype(int)

        # 4. MACD VARIANTS (30 features) - Enhanced
        macd_configs = [(5,13,5), (8,21,5), (12,26,9), (19,39,9), (6,12,6), (24,52,18), (7,14,7), (15,30,10)]
        for fast, slow, signal in macd_configs:
            macd_val, signal_val, hist_val = self.macd(close, fast, slow, signal)
            df[f'MACD_{fast}_{slow}'] = macd_val
            df[f'MACD_SIGNAL_{fast}_{slow}'] = signal_val
            df[f'MACD_HIST_{fast}_{slow}'] = hist_val

        # DiNapoli MACD[12]
        dinapoli_macd, dinapoli_signal, dinapoli_hist = self.dinapoli_macd(close)
        df['DINAPOLI_MACD'] = dinapoli_macd
        df['DINAPOLI_SIGNAL'] = dinapoli_signal
        df['DINAPOLI_HIST'] = dinapoli_hist

        # 5. BOLLINGER BANDS & VARIANTS (40 features) - Enhanced
        bb_configs = [(10,1.5), (10,2), (15,1.8), (20,1.5), (20,2), (20,2.5), (25,2), (30,2), (34,2), (50,2)]
        for period, std_dev in bb_configs:
            bb_upper, bb_middle, bb_lower = self.bollinger_bands(close, period, std_dev)
            df[f'BB_UPPER_{period}_{int(std_dev*10)}'] = bb_upper
            df[f'BB_LOWER_{period}_{int(std_dev*10)}'] = bb_lower
            df[f'BB_WIDTH_{period}_{int(std_dev*10)}'] = (bb_upper - bb_lower) / bb_middle
            df[f'BB_POSITION_{period}_{int(std_dev*10)}'] = (close - bb_lower) / (bb_upper - bb_lower)

        # Keltner Channels[12]
        keltner_periods = [10, 14, 20, 28]
        for period in keltner_periods:
            kc_upper, kc_middle, kc_lower = self.keltner_channels(high, low, close, period)
            df[f'KC_UPPER_{period}'] = kc_upper
            df[f'KC_MIDDLE_{period}'] = kc_middle
            df[f'KC_LOWER_{period}'] = kc_lower
            df[f'KC_WIDTH_{period}'] = (kc_upper - kc_lower) / kc_middle

        # 6. VOLATILITY INDICATORS (25 features) - Enhanced
        atr_periods = [7, 10, 14, 17, 20, 21, 28, 30, 34, 50]
        for period in atr_periods:
            df[f'ATR_{period}'] = self.atr(high, low, close, period)
            df[f'ATR_PERCENT_{period}'] = df[f'ATR_{period}'] / close * 100

        # Volatility measures
        for period in [5, 10, 14, 20, 30, 50]:
            df[f'VOLATILITY_{period}'] = close.rolling(period).std() / close.rolling(period).mean()
            df[f'PRICE_RANGE_{period}'] = (high.rolling(period).max() - low.rolling(period).min()) / close

        # 7. VOLUME INDICATORS (35 features) - Massively enhanced
        df['OBV'] = self.obv(close, volume)
        df['VWAP'] = self.vwap(high, low, close, volume)

        # Money Flow Index variants[12]
        mfi_periods = [10, 14, 17, 20, 25, 28]
        for period in mfi_periods:
            df[f'MFI_{period}'] = self.mfi(high, low, close, volume, period)

        # Chaikin indicators[12]
        df['CHAIKIN_OSC'] = self.chaikin_oscillator(high, low, close, volume)
        chaikin_periods = [10, 14, 20, 28]
        for period in chaikin_periods:
            df[f'CHAIKIN_MF_{period}'] = self.chaikin_money_flow(high, low, close, volume, period)

        # Volume analysis
        for period in [5, 10, 14, 15, 20, 25, 30, 50]:
            df[f'VOLUME_SMA_{period}'] = self.sma(volume, period)
            df[f'VOLUME_RATIO_{period}'] = volume / df[f'VOLUME_SMA_{period}']
            df[f'VOLUME_ROC_{period}'] = self.volume_rate_of_change(volume, period)

        # Volume oscillators
        vol_osc_configs = [(5,10), (10,20), (14,28)]
        for short, long in vol_osc_configs:
            df[f'VOLUME_OSC_{short}_{long}'] = self.volume_oscillator(volume, short, long)

        df['VOLUME_PRICE_TREND'] = ((close - close.shift(1)) / close.shift(1) * volume).cumsum()
        df['EASE_OF_MOVEMENT'] = ((high + low) / 2 - (high.shift(1) + low.shift(1)) / 2) * volume / (high - low)
        df['ACCUMULATION_DISTRIBUTION'] = ((close - low) - (high - close)) / (high - low) * volume
        df['ACCUMULATION_DISTRIBUTION'] = df['ACCUMULATION_DISTRIBUTION'].fillna(0).cumsum()

        # 8. PRICE ACTION & PATTERNS (40 features) - Enhanced
        df['TYPICAL_PRICE'] = (high + low + close) / 3
        df['MEDIAN_PRICE'] = (high + low) / 2
        df['WEIGHTED_CLOSE'] = (high + low + 2*close) / 4
        df['RANGE'] = high - low
        df['BODY'] = abs(close - open_price)
        df['UPPER_SHADOW'] = high - np.maximum(open_price, close)
        df['LOWER_SHADOW'] = np.minimum(open_price, close) - low
        df['BODY_RANGE_RATIO'] = df['BODY'] / df['RANGE']
        df['UPPER_SHADOW_RATIO'] = df['UPPER_SHADOW'] / df['RANGE']
        df['LOWER_SHADOW_RATIO'] = df['LOWER_SHADOW'] / df['RANGE']

        # Price ratios
        df['HL_RATIO'] = self.safe_divide(high, low)
        df['CO_RATIO'] = self.safe_divide(close, open_price)
        df['HC_RATIO'] = self.safe_divide(high, close)
        df['LC_RATIO'] = self.safe_divide(low, close)
        df['OC_RATIO'] = self.safe_divide(open_price, close)
        df['HO_RATIO'] = self.safe_divide(high, open_price)
        df['LO_RATIO'] = self.safe_divide(low, open_price)

        # Heikin Ashi[8][9]
        ha_open, ha_high, ha_low, ha_close = self.heikin_ashi(open_price, high, low, close)
        df['HA_OPEN'] = ha_open
        df['HA_HIGH'] = ha_high
        df['HA_LOW'] = ha_low
        df['HA_CLOSE'] = ha_close
        df['HA_BODY'] = abs(ha_close - ha_open)
        df['HA_UPPER_SHADOW'] = ha_high - np.maximum(ha_open, ha_close)
        df['HA_LOWER_SHADOW'] = np.minimum(ha_open, ha_close) - ha_low

        # Gap analysis
        df['GAP_UP'] = (low > high.shift(1)).astype(int)
        df['GAP_DOWN'] = (high < low.shift(1)).astype(int)
        df['GAP_SIZE'] = np.where(df['GAP_UP'], low - high.shift(1),
                                 np.where(df['GAP_DOWN'], low.shift(1) - high, 0))
        df['GAP_PERCENTAGE'] = df['GAP_SIZE'] / close.shift(1) * 100

        # Support/Resistance levels
        for window in [5, 8, 10, 13, 15, 20, 21, 25, 30, 34, 50, 55]:
            df[f'RESISTANCE_{window}'] = high.rolling(window).max()
            df[f'SUPPORT_{window}'] = low.rolling(window).min()
            df[f'RESISTANCE_DISTANCE_{window}'] = (df[f'RESISTANCE_{window}'] - close) / close
            df[f'SUPPORT_DISTANCE_{window}'] = (close - df[f'SUPPORT_{window}']) / close
            df[f'SUPPORT_RESISTANCE_RATIO_{window}'] = df[f'SUPPORT_DISTANCE_{window}'] / df[f'RESISTANCE_DISTANCE_{window}']

        # 9. ADVANCED MOMENTUM (30 features) - Enhanced
        momentum_periods = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 18, 20, 21, 25, 28, 30]
        for period in momentum_periods:
            df[f'MOMENTUM_{period}'] = close - close.shift(period)
            df[f'ROC_{period}'] = (close / close.shift(period) - 1) * 100

        # TRIX variants
        for period in [5, 8, 10, 14, 20, 28]:
            trix_val = self.tema(close, period).pct_change() * 10000
            df[f'TRIX_{period}'] = trix_val
            df[f'TRIX_SIGNAL_{period}'] = self.ema(trix_val, 9)

        # 10. HARMONIC PATTERNS (20 features) - NEW[10]
        harmonic_patterns = self.detect_harmonic_patterns(high, low, close)
        for pattern_name, pattern_values in harmonic_patterns.items():
            df[pattern_name] = pattern_values

        # 11. ELLIOTT WAVE RATIOS (15 features) - NEW[10]
        elliott_ratios = self.elliott_wave_ratios(close)
        for ratio_name, ratio_values in elliott_ratios.items():
            df[ratio_name] = ratio_values

        # 12. ADVANCED CANDLESTICK PATTERNS (50+ features) - MASSIVELY ENHANCED[8]
        advanced_patterns = self.advanced_candlestick_patterns(open_price, high, low, close)
        for pattern_name, pattern_values in advanced_patterns.items():
            df[pattern_name] = pattern_values

        # Basic patterns (keeping existing ones)
        df['DOJI'] = (abs(close - open_price) <= (high - low) * 0.1).astype(int)
        df['HAMMER'] = ((df['LOWER_SHADOW'] > 2 * df['BODY']) &
                       (df['UPPER_SHADOW'] < df['BODY'])).astype(int)
        df['BULLISH_ENGULFING'] = ((close > open_price) &
                                  (close.shift(1) < open_price.shift(1)) &
                                  (close > open_price.shift(1)) &
                                  (open_price < close.shift(1))).astype(int)
        df['BEARISH_ENGULFING'] = ((close < open_price) &
                                  (close.shift(1) > open_price.shift(1)) &
                                  (close < open_price.shift(1)) &
                                  (open_price > close.shift(1))).astype(int)

        # 13. FIBONACCI LEVELS (20 features) - Enhanced
        for window in [8, 13, 21, 34, 55]:
            swing_high = high.rolling(window).max()
            swing_low = low.rolling(window).min()
            fib_range = swing_high - swing_low

            df[f'FIB_23_6_{window}'] = swing_high - 0.236 * fib_range
            df[f'FIB_38_2_{window}'] = swing_high - 0.382 * fib_range
            df[f'FIB_50_0_{window}'] = swing_high - 0.500 * fib_range
            df[f'FIB_61_8_{window}'] = swing_high - 0.618 * fib_range
            df[f'FIB_78_6_{window}'] = swing_high - 0.786 * fib_range

            # Fibonacci retracement percentages[10]
            df[f'FIB_RETRACEMENT_{window}'] = (close - swing_low) / fib_range

        # 14. CORRELATION INDICATORS (15 features) - Enhanced
        for period in [5, 10, 14, 20, 30]:
            df[f'PRICE_VOLUME_CORR_{period}'] = close.rolling(period).corr(volume)
            df[f'HIGH_LOW_CORR_{period}'] = high.rolling(period).corr(low)
            df[f'OPEN_CLOSE_CORR_{period}'] = open_price.rolling(period).corr(close)

        # Cross-correlations
        df['RANGE_VOLUME_CORR_10'] = df['RANGE'].rolling(10).corr(volume)
        df['RANGE_VOLUME_CORR_20'] = df['RANGE'].rolling(20).corr(volume)
        df['BODY_VOLUME_CORR_10'] = df['BODY'].rolling(10).corr(volume)

        # Clean up inf/nan values
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)

        # Optimize data types
        df = self.optimize_dtypes(df)

        total_features = len(df.columns)
        technical_features = total_features - len(required_cols) - 2  # -2 for Target and Crypto_Pair
        print(f"✅ Generated {total_features} total columns ({technical_features} technical indicators)")

        return df

    def prepare_5min_data(self, df):
        """Prepare already 5-minute bar data - no conversion needed"""
        try:
            df = df.copy()
            # Handle both timestamp formats: epoch ms and datetime string
            if 'time' in df.columns:
                # New format: time,open,high,low,close,volume with datetime strings
                df['Datetime'] = pd.to_datetime(df['time'])
                df = df.drop('time', axis=1)
            elif 'Timestamp' in df.columns:
                # Old format: Timestamp as epoch ms
                df['Datetime'] = pd.to_datetime(df['Timestamp'], unit='ms')
                df = df.drop('Timestamp', axis=1)
            else:
                return None

            df.set_index('Datetime', inplace=True)
            df = df.dropna()
            return df if len(df) > 0 else None
        except Exception as e:
            print(f"    ⚠️ Error in prepare_5min_data: {e}")
            return None

    def create_target_variable(self, df, profit_threshold=0.015, loss_threshold=-0.015):
        df = df.copy()
        future_returns = []
        for i in range(len(df)):
            if i >= len(df) - 5:
                future_returns.append(0)
                continue
            current_price = df['Close'].iloc[i]
            target = 0
            for j in range(1, 6):
                if i + j < len(df):
                    future_price = df['Close'].iloc[i + j]
                    return_pct = (future_price - current_price) / current_price
                    if return_pct >= profit_threshold:
                        target = 1
                        break
                    elif return_pct <= loss_threshold:
                        target = 0
                        break
            future_returns.append(target)
        df['Target'] = future_returns
        return df

    # [Rest of your methods remain unchanged...]

# =========================
# 4. MODIFIED Optimized Data Processing for Separate Crypto Models
# =========================
def process_crypto_files_separate_models(data_path_drive, local_save_path, local_cache_path, scaler_save_path):
    """Process CSV files with MAXIMUM 300+ technical indicators + separate models per crypto"""
    analyzer = MaximumTechnicalAnalyzer(sequence_length=ULTRA_ENHANCED_CONFIG["sequence_length"])
    csv_files = glob.glob(os.path.join(data_path_drive, "*.csv"))

    os.makedirs(local_cache_path, exist_ok=True)
    os.makedirs(local_save_path, exist_ok=True)
    os.makedirs(scaler_save_path, exist_ok=True)

    processed_files = 0
    horizon = ULTRA_ENHANCED_CONFIG["prediction_horizon"]
    target_type = ULTRA_ENHANCED_CONFIG["horizon_type"]
    crypto_pairs = {}  # Store info about each crypto pair

    print(f"📁 Processing {len(csv_files)} CSV files with separate models per crypto")
    print(f"💾 Optimized for {ULTRA_ENHANCED_CONFIG['chunk_size']} chunk processing")
    print(f"🔧 Creating separate scalers per crypto pair")
    print(f"⏱️ Working directly with 5-minute bars (no conversion needed)")
    print(f"🧠 Memory-efficient: Handles multiple files per crypto (1ADAUSDT.csv, 2ADAUSDT.csv, etc.)")

    # Group files by crypto pair (handle multiple files per crypto)
    crypto_groups = {}
    for file_path in csv_files:
        filename = os.path.basename(file_path).replace('.csv', '')

        # Extract crypto name (remove numeric prefix if present)
        # Examples: "1ADAUSDT" -> "ADAUSDT", "BTCUSDT" -> "BTCUSDT"
        import re
        match = re.match(r'^(\d+)?(.+)$', filename)
        if match:
            crypto_name = match.group(2)  # Get the crypto part without prefix
        else:
            crypto_name = filename

        if crypto_name not in crypto_groups:
            crypto_groups[crypto_name] = []
        crypto_groups[crypto_name].append(file_path)

    print(f"📊 Found {len(crypto_groups)} unique crypto pairs:")
    for crypto_name, files in crypto_groups.items():
        print(f"  {crypto_name}: {len(files)} file(s)")

    # Process each crypto group separately
    for crypto_name, file_paths in crypto_groups.items():
        try:
            print(f"\n🔄 Processing {crypto_name} with {len(file_paths)} file(s) - separate model approach...")

            cached_filename = f"{crypto_name}_separate_model_{horizon}_{target_type}.parquet"
            cached_filepath = os.path.join(local_cache_path, cached_filename)
            scaler_filename = f"{crypto_name}_scaler.joblib"
            scaler_filepath = os.path.join(scaler_save_path, scaler_filename)

            if os.path.exists(cached_filepath) and os.path.exists(scaler_filepath):
                print(f"    Using cached data and scaler for {crypto_name}")
                crypto_pairs[crypto_name] = {
                    'data_file': cached_filepath,
                    'scaler_file': scaler_filepath,
                    'processed': True
                }
                processed_files += 1
                continue

            # Process files sequentially to avoid OOM (memory-efficient approach)
            print(f"    📁 Processing {len(file_paths)} files for {crypto_name} sequentially to avoid OOM...")

            # Create a temporary combined file to avoid memory issues
            temp_combined_file = os.path.join(local_cache_path, f"temp_{crypto_name}_combined.parquet")

            if os.path.exists(temp_combined_file):
                print(f"    � Using existing temporary combined file for {crypto_name}")
                combined_df = pd.read_parquet(temp_combined_file)
            else:
                print(f"    🔄 Creating temporary combined file for {crypto_name}...")
                processed_chunks = []
                total_rows = 0

                for i, file_path in enumerate(sorted(file_paths), 1):
                    try:
                        print(f"      Processing file {i}/{len(file_paths)}: {os.path.basename(file_path)}")
                        df = pd.read_csv(file_path)

                        # Handle different CSV formats
                        if len(df.columns) == 6:
                            if 'time' in df.columns:
                                df.columns = ['time', 'Open', 'High', 'Low', 'Close', 'Volume']
                            else:
                                df.columns = ['Timestamp', 'Open', 'Close', 'High', 'Low', 'Volume']
                                df = df[['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume']]
                        elif len(df.columns) == 7:
                            df = df.iloc[:, :-1]
                            if 'time' in df.columns:
                                df.columns = ['time', 'Open', 'High', 'Low', 'Close', 'Volume']
                            else:
                                df.columns = ['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume']
                        else:
                            print(f"        ❌ Unexpected columns in {os.path.basename(file_path)}: {len(df.columns)}")
                            continue

                        if len(df) < 50:
                            print(f"        ⚠️ Small file: {len(df)} rows")

                        # Process this chunk and save to temporary parquet
                        chunk_file = os.path.join(local_cache_path, f"temp_{crypto_name}_chunk_{i}.parquet")
                        df.to_parquet(chunk_file, compression="zstd", index=False)
                        processed_chunks.append(chunk_file)
                        total_rows += len(df)

                        print(f"        ✅ Processed: {len(df)} rows")

                        # Clear memory immediately
                        del df
                        gc.collect()

                    except Exception as e:
                        print(f"        ❌ Error processing {os.path.basename(file_path)}: {e}")
                        continue

                if not processed_chunks:
                    print(f"    ❌ No valid files processed for {crypto_name}")
                    continue

                # Process each chunk individually with indicators to avoid OOM
                print(f"    � Processing {len(processed_chunks)} chunks individually with indicators...")
                processed_indicator_chunks = []
                all_feature_cols = None

                for i, chunk_file in enumerate(processed_chunks, 1):
                    try:
                        print(f"      Processing chunk {i}/{len(processed_chunks)} with indicators...")

                        # Load chunk
                        chunk_df = pd.read_parquet(chunk_file)
                        os.remove(chunk_file)  # Clean up raw chunk immediately

                        # Process this chunk
                        df_5min = analyzer.prepare_5min_data(chunk_df)
                        del chunk_df
                        gc.collect()

                        if df_5min is None or len(df_5min) < 50:
                            print(f"        ❌ Failed to prepare 5min data for chunk {i}")
                            continue

                        print(f"        🔄 Adding indicators to chunk {i}...")
                        df_with_indicators = analyzer.add_maximum_technical_indicators(df_5min)
                        del df_5min
                        gc.collect()

                        if df_with_indicators is None:
                            print(f"        ❌ Failed to add indicators to chunk {i}")
                            continue

                        print(f"        🎯 Creating targets for chunk {i}...")
                        df_final = analyzer.create_multi_horizon_target(
                            df_with_indicators, horizon=horizon, target_type=target_type
                        )
                        del df_with_indicators
                        gc.collect()

                        if df_final is None or len(df_final) < 10:
                            print(f"        ❌ Failed to create targets for chunk {i}")
                            continue

                        df_final['Crypto_Pair'] = crypto_name

                        # Save processed chunk with indicators
                        indicator_chunk_file = os.path.join(local_cache_path, f"{crypto_name}_indicators_chunk_{i}.parquet")
                        df_final.to_parquet(indicator_chunk_file, compression="zstd", index=False)
                        processed_indicator_chunks.append(indicator_chunk_file)

                        # Store feature columns from first chunk
                        if all_feature_cols is None:
                            target_cols = [col for col in df_final.columns if col.startswith('Target_Step_')]
                            all_feature_cols = [col for col in df_final.columns if col not in target_cols + ['Crypto_Pair']]

                        print(f"        ✅ Chunk {i}: {len(df_final)} rows, {len(all_feature_cols)} features")

                        del df_final
                        gc.collect()

                    except Exception as e:
                        print(f"        ❌ Error processing chunk {i}: {e}")
                        continue

                if not processed_indicator_chunks:
                    print(f"    ❌ No chunks processed successfully for {crypto_name}")
                    continue

                # Create metadata file instead of combining all chunks
                chunk_metadata = {
                    'crypto_name': crypto_name,
                    'chunk_files': processed_indicator_chunks,
                    'num_chunks': len(processed_indicator_chunks),
                    'feature_cols': all_feature_cols,
                    'target_cols': [f'Target_Step_{i+1}' for i in range(horizon)],
                    'horizon': horizon,
                    'target_type': target_type
                }

                # Save metadata
                metadata_file = temp_combined_file.replace('.parquet', '_metadata.json')
                with open(metadata_file, 'w') as f:
                    json.dump(chunk_metadata, f, indent=2)

                print(f"    💾 Saved chunked metadata: {len(processed_indicator_chunks)} chunks")

                # Create a reference file instead of actual combined data
                chunk_info_df = pd.DataFrame({
                    'chunk_file': processed_indicator_chunks,
                    'crypto_pair': crypto_name
                })
                chunk_info_df.to_parquet(temp_combined_file, compression="zstd", index=False)

            # Load metadata to check if we have enough data
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                total_chunks = metadata['num_chunks']
                if total_chunks == 0:
                    print(f"    ❌ No chunks processed successfully for {crypto_name}")
                    continue
            else:
                print(f"    ❌ No metadata file found for {crypto_name}")
                continue

            # Create scaler by sampling from chunks (memory efficient)
            print(f"    🔧 Creating scaler from chunk samples...")
            sample_data = []
            sample_chunks = processed_indicator_chunks[:min(3, len(processed_indicator_chunks))]  # Use first 3 chunks max

            for chunk_file in sample_chunks:
                try:
                    # Read full chunk and then sample (parquet doesn't support nrows)
                    chunk_df = pd.read_parquet(chunk_file)
                    # Sample 1000 rows or all rows if less than 1000
                    sample_size = min(1000, len(chunk_df))
                    chunk_sample = chunk_df.sample(n=sample_size, random_state=42)
                    sample_data.append(chunk_sample[all_feature_cols].fillna(0))
                    del chunk_df, chunk_sample
                    gc.collect()
                except Exception as e:
                    print(f"        ⚠️ Error sampling from {chunk_file}: {e}")
                    continue

            if not sample_data:
                print(f"    ❌ Could not create scaler samples for {crypto_name}")
                continue

            scaler_data = pd.concat(sample_data, ignore_index=True)
            scaler = StandardScaler()
            scaler.fit(scaler_data)
            joblib.dump(scaler, scaler_filepath)
            print(f"    💾 Saved scaler for {crypto_name} (from {len(sample_data)} chunk samples)")

            del sample_data, scaler_data
            gc.collect()

            # Save the metadata file as the main data reference
            cached_filepath_metadata = cached_filepath.replace('.parquet', '_metadata.json')
            with open(cached_filepath_metadata, 'w') as f:
                json.dump(chunk_metadata, f, indent=2)

            # Create a symbolic file that points to the chunks
            chunk_info_df.to_parquet(cached_filepath, compression="zstd", index=False)

            # Calculate total rows from all chunks
            total_rows = 0
            for chunk_file in processed_indicator_chunks:
                try:
                    chunk_size = len(pd.read_parquet(chunk_file))
                    total_rows += chunk_size
                except:
                    pass

            crypto_pairs[crypto_name] = {
                'data_file': cached_filepath,
                'metadata_file': cached_filepath_metadata,
                'scaler_file': scaler_filepath,
                'processed': True,
                'rows': total_rows,
                'features': len(all_feature_cols),
                'targets': len(chunk_metadata['target_cols']),
                'chunks': len(processed_indicator_chunks),
                'chunk_files': processed_indicator_chunks
            }
            processed_files += 1

            print(f"    ✅ Success: {total_rows} total rows across {len(processed_indicator_chunks)} chunks")
            print(f"    📊 Features: {len(all_feature_cols)}, Targets: {len(chunk_metadata['target_cols'])}")
            print(f"    � Combined from {len(file_paths)} files for {crypto_name}")

            # Clean up temporary files after successful processing
            if os.path.exists(temp_combined_file):
                os.remove(temp_combined_file)
            if os.path.exists(metadata_file):
                os.remove(metadata_file)
            print(f"    🗑️ Cleaned up temporary files for {crypto_name}")

            del scaler
            gc.collect()

        except Exception as e:
            print(f"    ❌ Error processing {crypto_name}: {str(e)}")
            continue

    # Save crypto pairs info
    crypto_info_file = os.path.join(local_save_path, "crypto_pairs_info.json")
    with open(crypto_info_file, 'w') as f:
        json.dump(crypto_pairs, f, indent=2)

    print(f"\n✅ Processed {processed_files} crypto pairs with separate models")
    print(f"📊 Crypto pairs info saved to: {crypto_info_file}")

    # Print summary
    for crypto_name, info in crypto_pairs.items():
        if info.get('processed'):
            print(f"  {crypto_name}: {info.get('rows', 0)} rows, {info.get('features', 0)} features")

    return processed_files > 0, crypto_pairs

# =========================
# 5. MODIFIED Optimized Chunk Generator for Separate Crypto Models
# =========================
class OptimizedSeparateCryptoChunkGenerator:
    def __init__(self, data_file, scaler_file, current_chunk=0, sequence_length=60,
                 validation_split=0.2, is_training=True, prediction_horizon=10):
        self.data_file = data_file  # Reference file for chunked crypto
        self.scaler_file = scaler_file
        self.chunk_size = ULTRA_ENHANCED_CONFIG["chunk_size"]
        self.current_chunk = current_chunk
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon

        # Load metadata to get chunk files
        metadata_file = data_file.replace('.parquet', '_metadata.json')
        if os.path.exists(metadata_file):
            with open(metadata_file, 'r') as f:
                self.metadata = json.load(f)
            self.chunk_files = self.metadata['chunk_files']
            self.is_chunked = True
        else:
            # Fallback to single file
            self.chunk_files = [data_file]
            self.is_chunked = False
        self.batch_size = ULTRA_ENHANCED_CONFIG["batch_size"] * strategy.num_replicas_in_sync
        self.validation_split = validation_split
        self.is_training = is_training
        self.scaler = None

        self._load_optimized_chunk_data()
        self._build_optimized_dataset()

    def _load_optimized_chunk_data(self):
        print(f"🔄 Loading Crypto-Specific Chunk {self.current_chunk + 1} ({self.prediction_horizon} steps)")

        # Load scaler for this crypto
        self.scaler = joblib.load(self.scaler_file)

        if self.is_chunked:
            # Handle chunked data files
            print(f"    📁 Loading from {len(self.chunk_files)} chunked files...")

            # Calculate which chunk file(s) to use for this training chunk
            total_sequences = 0
            chunk_sequence_counts = []

            # First pass: count sequences in each chunk file
            for chunk_file in self.chunk_files:
                try:
                    chunk_df = pd.read_parquet(chunk_file)
                    sequences_in_chunk = max(0, len(chunk_df) - self.sequence_length)
                    chunk_sequence_counts.append(sequences_in_chunk)
                    total_sequences += sequences_in_chunk
                    del chunk_df
                    gc.collect()
                except Exception as e:
                    print(f"    ⚠️ Error reading chunk file {chunk_file}: {e}")
                    chunk_sequence_counts.append(0)

            if total_sequences == 0:
                self.X_arr = np.array([])
                self.y_arr = np.array([])
                return

            global_start = self.current_chunk * self.chunk_size
            global_end = min(global_start + self.chunk_size, total_sequences)

            if global_start >= global_end:
                self.X_arr = np.array([])
                self.y_arr = np.array([])
                return

            # Find which chunk files contain our target sequences
            cum_sequences = np.concatenate(([0], np.cumsum(chunk_sequence_counts)))

            # Load relevant chunk files
            chunk_dfs = []
            for i, (chunk_file, start_seq, end_seq) in enumerate(zip(self.chunk_files, cum_sequences[:-1], cum_sequences[1:])):
                if end_seq <= global_start or start_seq >= global_end:
                    continue  # Skip chunks outside our range

                try:
                    chunk_df = pd.read_parquet(chunk_file)

                    # Calculate local indices within this chunk
                    local_start = max(0, global_start - start_seq)
                    local_end = min(end_seq - start_seq, global_end - start_seq)

                    if local_end > local_start:
                        # Add sequence_length to ensure we have enough data for sequences
                        slice_end = min(len(chunk_df), local_end + self.sequence_length)
                        chunk_slice = chunk_df.iloc[local_start:slice_end].copy()
                        chunk_dfs.append(chunk_slice)

                    del chunk_df
                    gc.collect()

                except Exception as e:
                    print(f"    ⚠️ Error processing chunk file {chunk_file}: {e}")
                    continue

            if not chunk_dfs:
                self.X_arr = np.array([])
                self.y_arr = np.array([])
                return

            # Combine relevant chunks
            chunk_df = pd.concat(chunk_dfs, ignore_index=True)
            del chunk_dfs
            gc.collect()

        else:
            # Handle single file (fallback)
            df = pd.read_parquet(self.data_file)

            total_sequences = max(0, len(df) - self.sequence_length)
            if total_sequences == 0:
                self.X_arr = np.array([])
                self.y_arr = np.array([])
                return

            global_start = self.current_chunk * self.chunk_size
            global_end = min(global_start + self.chunk_size, total_sequences)

            if global_start >= global_end:
                self.X_arr = np.array([])
                self.y_arr = np.array([])
                return

            # Extract chunk from single crypto data
            slice_end = global_end + self.sequence_length
            chunk_df = df.iloc[global_start:slice_end].copy()
            del df
            gc.collect()

        # Separate features and targets
        target_cols = [col for col in chunk_df.columns if col.startswith('Target_Step_')]
        excl = set(target_cols) | {'Crypto_Pair'}
        self.feats = [c for c in chunk_df.columns if c not in excl]

        feats_np = chunk_df[self.feats].fillna(0).to_numpy(np.float32)
        targets_np = chunk_df[target_cols].to_numpy(np.float32)

        print(f"🔥 Processing {feats_np.shape} features with {targets_np.shape} crypto-specific targets...")

        # Use pre-fitted scaler for this crypto
        feats_np = self.scaler.transform(feats_np).astype(np.float16)
        feats_np = np.clip(feats_np, -5, 5)

        current_sequences = min(self.chunk_size, len(targets_np) - self.sequence_length)
        if current_sequences <= 0:
            self.X_arr = np.array([])
            self.y_arr = np.array([])
            return

        # Generate sequences
        st = feats_np.strides
        X = np.lib.stride_tricks.as_strided(
            feats_np[:current_sequences + self.sequence_length],
            shape=(current_sequences, self.sequence_length, feats_np.shape[1]),
            strides=(st[0], st[0], st[1])
        )
        y = targets_np[self.sequence_length:self.sequence_length + current_sequences]

        self.X_arr, self.y_arr = X, y

        memory_usage = (X.nbytes + y.nbytes) / 2**30
        print(f"✅ Crypto-specific chunk {self.current_chunk + 1}: {len(y):,} seqs | {len(self.feats)} features | {self.prediction_horizon} targets | {memory_usage:.2f} GB")

        del chunk_df
        gc.collect()

    def _build_optimized_dataset(self):
        if len(self.y_arr) == 0:
            self.dataset = None
            return

        # Split for training/validation
        split_idx = int(len(self.y_arr) * (1 - self.validation_split))
        if self.is_training:
            X_data, y_data = self.X_arr[:split_idx], self.y_arr[:split_idx]
        else:
            X_data, y_data = self.X_arr[split_idx:], self.y_arr[split_idx:]

        if len(y_data) == 0:
            self.dataset = None
            return

        # Optimized tf.data pipeline
        ds = tf.data.Dataset.from_tensor_slices((X_data, y_data))
        if self.is_training:
            ds = ds.shuffle(25000, reshuffle_each_iteration=True)
        ds = ds.batch(self.batch_size, drop_remainder=False)
        ds = ds.prefetch(tf.data.AUTOTUNE)
        ds = ds.cache()

        self.dataset = ds
        self.n_sequences = len(y_data)
        print(f"🚀 Built crypto-specific dataset: {self.n_sequences:,} sequences")

    def get_dataset_and_steps(self):
        if self.dataset is None:
            return None, 0
        steps = max(1, int(np.ceil(self.n_sequences / self.batch_size)))
        return self.dataset, steps

# =========================
# 6. MODIFIED Maximum Performance Trainer for Separate Crypto Models
# =========================
class MaximumPerformanceSeparateCryptoTrainer:
    def __init__(self, model_save_path, sequence_length=60, prediction_horizon=10):
        self.model_save_path = model_save_path
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.chunk_size = ULTRA_ENHANCED_CONFIG["chunk_size"]
        self.batch_size = ULTRA_ENHANCED_CONFIG["batch_size"]
        self.horizon_type = ULTRA_ENHANCED_CONFIG["horizon_type"]
        self.training_state_file = f"{model_save_path}/separate_crypto_models_{prediction_horizon}_state.json"
        os.makedirs(self.model_save_path, exist_ok=True)

    def build_crypto_specific_model(self, input_shape, crypto_name):
        """Build LSTM model for specific crypto pair"""
        print(f"🏗️  Building Crypto-Specific LSTM model for {crypto_name}")
        print(f"📊 Input shape: {input_shape} (300+ indicators)")
        print(f"🎯 Output: {self.prediction_horizon} future steps ({self.horizon_type})")

        with strategy.scope():
            model = Sequential([
                # Enhanced architecture for crypto-specific prediction
                Bidirectional(LSTM(128, return_sequences=True, dropout=0.2, recurrent_dropout=0.2),
                              name=f'{crypto_name}_bi_lstm_128'),
                BatchNormalization(),
                SpatialDropout1D(0.1),

                LSTM(64, return_sequences=True, dropout=0.1, recurrent_dropout=0.1,
                     name=f'{crypto_name}_lstm_64'),
                BatchNormalization(),
                SpatialDropout1D(0.1),

                LSTM(32, return_sequences=True, dropout=0.1, recurrent_dropout=0.1,
                     name=f'{crypto_name}_lstm_32'),
                BatchNormalization(),

                LSTM(16, return_sequences=False, dropout=0.1, name=f'{crypto_name}_lstm_16'),
                BatchNormalization(),

                # Crypto-specific prediction layers
                Dense(64, activation='relu', name=f'{crypto_name}_dense_64'),
                Dropout(0.2),
                Dense(32, activation='relu', name=f'{crypto_name}_dense_32'),
                Dropout(0.15),
                Dense(16, activation='relu', name=f'{crypto_name}_dense_16'),
                Dropout(0.1),

                # Output layer - crypto-specific
                Dense(self.prediction_horizon,
                      activation='linear' if self.horizon_type == 'regression' else 'sigmoid',
                      dtype='float32',
                      name=f'{crypto_name}_output_{self.prediction_horizon}')
            ])

            # Optimized learning rate
            base_lr = 0.001
            scaled_lr = base_lr * np.sqrt(self.batch_size / 512)

            optimizer = Adam(
                learning_rate=scaled_lr,
                clipnorm=1.0,
                beta_1=0.9,
                beta_2=0.999,
                epsilon=1e-7
            )

            # Loss function based on prediction type
            if self.horizon_type == 'regression':
                loss_fn = 'mse'
                metrics = ['mae']
            else:
                loss_fn = 'binary_crossentropy'
                metrics = ['accuracy']

            model.compile(
                optimizer=optimizer,
                loss=loss_fn,
                metrics=metrics
            )

        print(f"✅ Crypto-specific model built for {crypto_name}: {self.prediction_horizon} outputs")
        return model

    def save_training_state(self, crypto_name, chunk_idx, model_path, metrics, completed_chunks, total_chunks):
        state = {
            'crypto_name': crypto_name,
            'current_chunk': chunk_idx,
            'model_path': model_path,
            'metrics': metrics,
            'completed_chunks': completed_chunks,
            'total_chunks': total_chunks,
            'config': ULTRA_ENHANCED_CONFIG,
            'prediction_horizon': self.prediction_horizon,
            'horizon_type': self.horizon_type,
            'timestamp': datetime.now().isoformat(),
            'architecture': f'Crypto-Specific-LSTM-{self.prediction_horizon}steps',
            'indicators': '300+ MAXIMUM technical indicators',
            'optimization': 'SEPARATE_CRYPTO_OPTIMIZED'
        }
        crypto_state_file = f"{self.model_save_path}/{crypto_name}_training_state.json"
        with open(crypto_state_file, 'w') as f:
            json.dump(state, f, indent=2)
        print(f"💾 {crypto_name} state saved: {chunk_idx + 1}/{total_chunks}")
        gc.collect()

    def load_training_state(self, crypto_name):
        crypto_state_file = f"{self.model_save_path}/{crypto_name}_training_state.json"
        if os.path.exists(crypto_state_file):
            with open(crypto_state_file, 'r') as f:
                state = json.load(f)
            print(f"📂 Found {crypto_name} state: {len(state.get('completed_chunks', []))}/{state['total_chunks']} chunks")
            return state
        return None

    def calculate_total_chunks(self, data_file):
        # Check if this is a chunked data file
        metadata_file = data_file.replace('.parquet', '_metadata.json')
        if os.path.exists(metadata_file):
            # Chunked data - calculate from all chunk files
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)

            total_sequences = 0
            for chunk_file in metadata['chunk_files']:
                try:
                    chunk_df = pd.read_parquet(chunk_file)
                    n_sequences = max(0, len(chunk_df) - self.sequence_length)
                    total_sequences += n_sequences
                    del chunk_df
                    gc.collect()
                except Exception as e:
                    print(f"    ⚠️ Error calculating chunks for {chunk_file}: {e}")
                    continue
        else:
            # Single file
            df = pd.read_parquet(data_file)
            total_sequences = max(0, len(df) - self.sequence_length)
            del df
            gc.collect()

        return max(1, int(np.ceil(total_sequences / self.chunk_size)))

    def train_crypto_chunk(self, crypto_name, data_file, scaler_file, chunk_idx, total_chunks, model=None):
        print(f"\n🚀 {crypto_name} Training Chunk {chunk_idx + 1}/{total_chunks} ({self.prediction_horizon} steps)")

        train_gen = OptimizedSeparateCryptoChunkGenerator(
            data_file, scaler_file, current_chunk=chunk_idx,
            sequence_length=self.sequence_length,
            prediction_horizon=self.prediction_horizon,
            validation_split=0.2, is_training=True)
        val_gen = OptimizedSeparateCryptoChunkGenerator(
            data_file, scaler_file, current_chunk=chunk_idx,
            sequence_length=self.sequence_length,
            prediction_horizon=self.prediction_horizon,
            validation_split=0.2, is_training=False)

        train_ds, train_steps = train_gen.get_dataset_and_steps()
        val_ds, val_steps = val_gen.get_dataset_and_steps()

        if train_ds is None:
            print(f"⚠️  No data in chunk {chunk_idx + 1}")
            return model, None

        if model is None:
            input_shape = (self.sequence_length, len(train_gen.feats))
            model = self.build_crypto_specific_model(input_shape, crypto_name)
            print(f"🏗️  Built {crypto_name} model: {len(train_gen.feats)} → {self.prediction_horizon}")

        # Enhanced callbacks for crypto-specific training
        callbacks = [
            EarlyStopping(patience=3, restore_best_weights=True,
                         monitor='val_loss', verbose=1),
            ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=2,
                            min_lr=1e-7, verbose=1),
            ModelCheckpoint(
                f"{self.model_save_path}/{crypto_name}_checkpoint_{chunk_idx+1:02d}.keras",
                monitor='val_loss', save_best_only=True, verbose=1)
        ]

        print(f"🚀 {crypto_name} training: {train_steps} steps/epoch, batch={self.batch_size}")
        start_time = datetime.now()

        history = model.fit(
            train_ds,
            validation_data=val_ds,
            epochs=5,  # Slightly more epochs for multi-horizon
            steps_per_epoch=train_steps,
            validation_steps=val_steps,
            callbacks=callbacks,
            verbose=1
        )

        end_time = datetime.now()
        chunk_time = (end_time - start_time).total_seconds() / 60

        # Enhanced metrics for multi-horizon
        if self.horizon_type == 'regression':
            final_metric = history.history['val_mae'][-1]
            metric_name = 'val_mae'
        else:
            final_metric = history.history['val_accuracy'][-1]
            metric_name = 'val_accuracy'

        metrics = {
            'crypto_name': crypto_name,
            'chunk_idx': chunk_idx,
            'training_time_minutes': float(chunk_time),
            'final_loss': float(history.history['loss'][-1]),
            'final_val_loss': float(history.history['val_loss'][-1]),
            f'final_{metric_name}': float(final_metric),
            'prediction_horizon': self.prediction_horizon,
            'horizon_type': self.horizon_type,
            'steps_per_epoch': train_steps,
            'features_count': len(train_gen.feats),
            'sequences_processed': train_gen.n_sequences + val_gen.n_sequences,
            'chunk_size': self.chunk_size
        }

        print(f"✅ {crypto_name} chunk {chunk_idx + 1} completed in {chunk_time:.1f} minutes")
        print(f"   🎯 {metric_name}: {final_metric:.4f}")
        print(f"   📊 Indicators: {metrics['features_count']}")
        print(f"   ⚡ Crypto-specific: {self.prediction_horizon} steps ahead")

        # Save model in .keras format
        chunk_model_path = f"{self.model_save_path}/{crypto_name}_model_{self.prediction_horizon}steps_chunk_{chunk_idx + 1:02d}.keras"
        model.save(chunk_model_path)

        del train_gen, val_gen
        gc.collect()

        return model, metrics

    def start_separate_crypto_training(self, crypto_pairs):
        print(f"🚀 STARTING SEPARATE CRYPTO TRAINING ({self.prediction_horizon} steps ahead)")
        print(f"📊 Configuration: {ULTRA_ENHANCED_CONFIG['chunk_size']} chunks with 300+ indicators")
        print(f"🎯 Target: {self.horizon_type} prediction for {self.prediction_horizon} future steps")
        print(f"💎 Training {len(crypto_pairs)} separate crypto models")

        all_models = {}
        all_metrics = {}
        total_training_time = 0

        for crypto_name, crypto_info in crypto_pairs.items():
            if not crypto_info.get('processed'):
                print(f"⚠️ Skipping {crypto_name} - not processed")
                continue

            print(f"\n{'='*60}")
            print(f"🚀 TRAINING {crypto_name.upper()} MODEL")
            print(f"{'='*60}")

            data_file = crypto_info['data_file']
            scaler_file = crypto_info['scaler_file']

            total_chunks = self.calculate_total_chunks(data_file)
            print(f"📊 {crypto_name} total chunks: {total_chunks}")

            state = self.load_training_state(crypto_name)
            if state and state['total_chunks'] == total_chunks and state.get('prediction_horizon') == self.prediction_horizon:
                model = load_model(state['model_path'])
                completed_chunks = state['completed_chunks']
                crypto_metrics = state['metrics']
                start_chunk = len(completed_chunks)
                print(f"🔄 Resuming {crypto_name} training from chunk {start_chunk + 1}")
            else:
                model = None
                completed_chunks = []
                crypto_metrics = []
                start_chunk = 0
                print(f"🆕 Starting fresh {crypto_name} training")

            crypto_time = 0
            crypto_sequences = 0

            for chunk_idx in range(start_chunk, total_chunks):
                model, metrics = self.train_crypto_chunk(
                    crypto_name, data_file, scaler_file, chunk_idx, total_chunks, model)
                if metrics:
                    crypto_metrics.append(metrics)
                    completed_chunks.append(chunk_idx)
                    crypto_time += metrics['training_time_minutes']
                    crypto_sequences += metrics['sequences_processed']

                    chunk_model_path = f"{self.model_save_path}/{crypto_name}_model_{self.prediction_horizon}steps_chunk_{chunk_idx + 1:02d}.keras"
                    self.save_training_state(crypto_name, chunk_idx, chunk_model_path, crypto_metrics, completed_chunks, total_chunks)

                    avg_time = crypto_time / len(completed_chunks)
                    print(f"\n📊 {crypto_name} Progress: {len(completed_chunks)}/{total_chunks} chunks")
                    print(f"⏱️  {crypto_name} time: {crypto_time:.1f} min | Avg: {avg_time:.1f} min/chunk")
                    print(f"📈 {crypto_name} sequences: {crypto_sequences:,}")

            # Save final model for this crypto
            final_model_path = f"{self.model_save_path}/final_{crypto_name}_{self.prediction_horizon}steps_model.keras"
            model.save(final_model_path)
            print(f"\n🎉 {crypto_name.upper()} TRAINING COMPLETED!")
            print(f"💾 Final {crypto_name} model: {final_model_path}")

            all_models[crypto_name] = {
                'model': model,
                'model_path': final_model_path,
                'scaler_path': scaler_file
            }
            all_metrics[crypto_name] = crypto_metrics
            total_training_time += crypto_time

            # Print crypto-specific summary
            if crypto_metrics:
                if self.horizon_type == 'regression':
                    avg_performance = np.mean([m['final_val_mae'] for m in crypto_metrics])
                    performance_name = 'Average MAE'
                else:
                    avg_performance = np.mean([m['final_val_accuracy'] for m in crypto_metrics])
                    performance_name = 'Average Accuracy'

                print(f"🏆 {crypto_name} {performance_name}: {avg_performance:.4f}")
                print(f"⏱️  {crypto_name} training time: {crypto_time:.1f} minutes")

        # Save overall summary
        summary = {
            'total_cryptos': len(all_models),
            'total_training_time': total_training_time,
            'prediction_horizon': self.prediction_horizon,
            'horizon_type': self.horizon_type,
            'config': ULTRA_ENHANCED_CONFIG,
            'models': {name: info['model_path'] for name, info in all_models.items()},
            'scalers': {name: info['scaler_path'] for name, info in all_models.items()},
            'timestamp': datetime.now().isoformat()
        }

        summary_file = f"{self.model_save_path}/training_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)

        print(f"\n{'='*60}")
        print(f"🎉 ALL CRYPTO TRAINING COMPLETED!")
        print(f"{'='*60}")
        print(f"💎 Trained models: {len(all_models)}")
        print(f"⏱️  Total training time: {total_training_time:.1f} minutes")
        print(f"💾 Summary saved: {summary_file}")

        return all_models, all_metrics

# =========================
# 7. MODIFIED Main Execution Function for Separate Crypto Models
# =========================
def main_separate_crypto_models():
    print("🚀 SEPARATE CRYPTO MODELS ML PIPELINE")
    print(f"📊 Features: 300+ technical indicators + {ULTRA_ENHANCED_CONFIG['prediction_horizon']}-step prediction")
    print(f"🏗️  Architecture: Separate LSTM per crypto ({ULTRA_ENHANCED_CONFIG['horizon_type']})")
    print(f"📦 Chunk Size: {ULTRA_ENHANCED_CONFIG['chunk_size']} sequences")
    print(f"🎯 Prediction: {ULTRA_ENHANCED_CONFIG['prediction_horizon']} steps ahead")
    print(f"💎 Separate models and scalers per crypto pair")

    # Path configuration
    DRIVE_CSV_PATH = "/content/drive/MyDrive/crypto_data"
    LOCAL_PROCESSED_PATH = "/content/crypto_processed_separate"
    LOCAL_CACHE_PATH = "/content/crypto_cache_separate"
    SCALER_SAVE_PATH = "/content/drive/MyDrive/crypto_scalers"
    MODEL_SAVE_PATH = "/content/drive/MyDrive/crypto_models_separate"

    # Create directories
    for path in [LOCAL_PROCESSED_PATH, LOCAL_CACHE_PATH, SCALER_SAVE_PATH, MODEL_SAVE_PATH]:
        os.makedirs(path, exist_ok=True)

    # Check CSV files
    csv_files = glob.glob(os.path.join(DRIVE_CSV_PATH, "*.csv"))
    if not csv_files:
        print(f"❌ No CSV files found in {DRIVE_CSV_PATH}")
        return False

    print(f"✅ Found {len(csv_files)} CSV files for separate crypto processing")

    print("\n" + "="*70)
    print(f"STEP 1: SEPARATE CRYPTO DATA PROCESSING ({ULTRA_ENHANCED_CONFIG['prediction_horizon']} steps)")
    print("="*70)

    success, crypto_pairs = process_crypto_files_separate_models(
        DRIVE_CSV_PATH, LOCAL_PROCESSED_PATH, LOCAL_CACHE_PATH, SCALER_SAVE_PATH)

    if not success:
        print("❌ Separate crypto data processing failed!")
        return False

    if not crypto_pairs:
        print("❌ No crypto pairs processed!")
        return False

    print(f"✅ Processed {len(crypto_pairs)} crypto pairs with separate scalers")

    print("\n" + "="*70)
    print(f"STEP 2: SEPARATE CRYPTO TRAINING ({ULTRA_ENHANCED_CONFIG['prediction_horizon']} steps ahead)")
    print("="*70)

    trainer = MaximumPerformanceSeparateCryptoTrainer(
        MODEL_SAVE_PATH,
        sequence_length=ULTRA_ENHANCED_CONFIG["sequence_length"],
        prediction_horizon=ULTRA_ENHANCED_CONFIG['prediction_horizon']
    )
    models, metrics = trainer.start_separate_crypto_training(crypto_pairs)

    if models:
        print("\n" + "="*70)
        print("SEPARATE CRYPTO MODELS RESULTS SUMMARY")
        print("="*70)

        total_time = 0
        total_features = 0
        all_performances = []

        for crypto_name, crypto_metrics in metrics.items():
            if crypto_metrics:
                crypto_time = sum(m['training_time_minutes'] for m in crypto_metrics)
                total_time += crypto_time

                if total_features == 0:
                    total_features = crypto_metrics[0]['features_count']

                if ULTRA_ENHANCED_CONFIG['horizon_type'] == 'regression':
                    avg_performance = np.mean([m['final_val_mae'] for m in crypto_metrics])
                    performance_name = 'MAE'
                else:
                    avg_performance = np.mean([m['final_val_accuracy'] for m in crypto_metrics])
                    performance_name = 'Accuracy'

                all_performances.append(avg_performance)
                print(f"💎 {crypto_name}: {performance_name} = {avg_performance:.4f}, Time = {crypto_time:.1f} min")

        overall_performance = np.mean(all_performances) if all_performances else 0

        print(f"\n🎯 Technical Indicators: {total_features}")
        print(f"� Crypto Models Trained: {len(models)}")
        print(f"🚀 Prediction Horizon: {ULTRA_ENHANCED_CONFIG['prediction_horizon']} steps ahead")
        print(f"📊 Prediction Type: {ULTRA_ENHANCED_CONFIG['horizon_type']}")
        print(f"⏱️  Total Training Time: {total_time:.1f} minutes")
        print(f"🏆 Overall Average {performance_name}: {overall_performance:.4f}")
        print(f"💾 Configuration: {ULTRA_ENHANCED_CONFIG}")
        print(f"✅ Separate crypto models and scalers saved in .keras/.joblib format!")

    return len(models) > 0 if models else False

# =========================
# 8. USAGE EXAMPLES AND CONFIGURATION
# =========================
def configure_prediction_horizon(horizon_steps, horizon_type="regression"):
    """
    Configure the prediction horizon

    Args:
        horizon_steps: Number of steps to predict ahead (1-50)
        horizon_type: "regression" for price prediction or "classification" for signals
    """
    ULTRA_ENHANCED_CONFIG["prediction_horizon"] = horizon_steps
    ULTRA_ENHANCED_CONFIG["horizon_type"] = horizon_type

    print(f"🎯 Configured for {horizon_steps}-step {horizon_type} prediction")
    return ULTRA_ENHANCED_CONFIG

# =========================
# 9. Execute Separate Crypto Models Pipeline
# =========================
print("🚀 LAUNCHING SEPARATE CRYPTO MODELS FORECASTING PIPELINE...")
print("✨ ENHANCED FEATURES:")
print(f"   📊 300+ technical indicators (preserved)")
print(f"   💎 Separate neural network per crypto pair")
print(f"   🔧 Individual scaler per crypto pair")
print(f"   ⏱️ Direct 5-minute bar processing (no conversion needed)")
print(f"   🎯 Configurable prediction horizon: {ULTRA_ENHANCED_CONFIG['prediction_horizon']} steps")
print(f"   📈 Prediction type: {ULTRA_ENHANCED_CONFIG['horizon_type']}")
print(f"   💾 Optimized chunk processing: {ULTRA_ENHANCED_CONFIG['chunk_size']}")
print(f"   🏗️  Crypto-specific LSTM architecture")
print(f"   💾 Native .keras model format + .joblib scalers")
print(f"   🔄 Auto-resume capability per crypto")
print(f"   🎯 TARGET: Crypto-specific forecasting with maximum indicators")
print(f"   📅 NEW: Support for datetime format (time,open,high,low,close,volume)")

# Example configurations:
print("\n📋 CONFIGURATION EXAMPLES:")
print("# 5-step price prediction:")
print("configure_prediction_horizon(5, 'regression')")
print("\n# 10-step signal classification:")
print("configure_prediction_horizon(10, 'classification')")
print("\n# 24-step (1 day) price forecasting:")
print("configure_prediction_horizon(24, 'regression')")

try:
    if main_separate_crypto_models():
        print("\n🎉 SEPARATE CRYPTO MODELS SUCCESS!")
        print("\n📊 ACHIEVEMENTS:")
        print("   ✅ 300+ technical indicators preserved")
        print(f"   ✅ {ULTRA_ENHANCED_CONFIG['prediction_horizon']}-step prediction implemented")
        print("   ✅ Separate neural network per crypto pair")
        print("   ✅ Individual scaler per crypto pair")
        print("   ✅ Direct 5-minute bar processing (no conversion)")
        print("   ✅ Support for new datetime CSV format")
        print("   ✅ Configurable horizon (1-50 steps)")
        print("   ✅ Both regression and classification support")
        print("   ✅ Models saved in .keras format")
        print("   ✅ Scalers saved in .joblib format")
        print("   ✅ Optimized chunk processing maintained")
        print("   ✅ Auto-resume training capability per crypto")
        print("\n💡 You now have separate specialized models for each crypto pair!")

        print(f"\n🔮 INFERENCE USAGE:")
        print(f"import tensorflow as tf")
        print(f"import joblib")
        print(f"")
        print(f"# Load specific crypto model and scaler")
        print(f"crypto_name = 'BTCUSDT'  # or any other crypto")
        print(f"model = tf.keras.models.load_model(f'final_{{crypto_name}}_{ULTRA_ENHANCED_CONFIG['prediction_horizon']}steps_model.keras')")
        print(f"scaler = joblib.load(f'{{crypto_name}}_scaler.joblib')")
        print(f"")
        print(f"# Preprocess your data with the crypto-specific scaler")
        print(f"scaled_features = scaler.transform(your_features)")
        print(f"")
        print(f"# Make predictions")
        print(f"# Input: (batch_size, {ULTRA_ENHANCED_CONFIG['sequence_length']}, n_features)")
        print(f"# Output: (batch_size, {ULTRA_ENHANCED_CONFIG['prediction_horizon']}) - {ULTRA_ENHANCED_CONFIG['prediction_horizon']} future steps")
        print(f"predictions = model.predict(scaled_features)")
    else:
        print("\n❌ SEPARATE CRYPTO MODELS PIPELINE FAILED!")
except Exception as e:
    print(f"\n❌ CRITICAL ERROR: {str(e)}")
    import traceback
    traceback.print_exc()

#!/usr/bin/env python3
"""
Setup script for the Crypto Trading Bot
Helps users configure the bot and verify everything is working
"""

import os
import sys
import subprocess
import configparser
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def check_models_and_scalers():
    """Check if models and scalers are available"""
    print("🔍 Checking for trained models and scalers...")
    
    # Read config to get paths
    config = configparser.ConfigParser()
    config.read('trading_config.ini')
    
    models_path = config.get('paths', 'models_path', fallback='/content/drive/MyDrive/crypto_models_separate')
    scalers_path = config.get('paths', 'scalers_path', fallback='/content/drive/MyDrive/crypto_scalers')
    
    models_found = 0
    scalers_found = 0
    
    if os.path.exists(models_path):
        models = [f for f in os.listdir(models_path) if f.endswith('.keras')]
        models_found = len(models)
        print(f"📊 Found {models_found} model files in {models_path}")
    else:
        print(f"⚠️ Models path not found: {models_path}")
    
    if os.path.exists(scalers_path):
        scalers = [f for f in os.listdir(scalers_path) if f.endswith('.joblib')]
        scalers_found = len(scalers)
        print(f"🔧 Found {scalers_found} scaler files in {scalers_path}")
    else:
        print(f"⚠️ Scalers path not found: {scalers_path}")
    
    if models_found == 0 or scalers_found == 0:
        print("❌ No models or scalers found!")
        print("Please run the training script (a1.py) first to generate models and scalers.")
        return False
    
    print("✅ Models and scalers found")
    return True

def setup_config():
    """Setup configuration file"""
    print("⚙️ Setting up configuration...")
    
    if not os.path.exists('trading_config.ini'):
        print("📝 Creating default configuration file...")
        from crypto_trading_bot import create_default_config
        create_default_config()
    
    # Read current config
    config = configparser.ConfigParser()
    config.read('trading_config.ini')
    
    # Check if API keys are set
    api_key = config.get('binance', 'api_key', fallback='')
    if api_key == 'YOUR_BINANCE_API_KEY' or not api_key:
        print("⚠️ Binance API credentials not configured!")
        print("Please edit trading_config.ini and add your API credentials.")
        
        setup_api = input("Would you like to set up API credentials now? (y/n): ").lower()
        if setup_api == 'y':
            api_key = input("Enter your Binance API Key: ").strip()
            api_secret = input("Enter your Binance API Secret: ").strip()
            
            config.set('binance', 'api_key', api_key)
            config.set('binance', 'api_secret', api_secret)
            
            with open('trading_config.ini', 'w') as f:
                config.write(f)
            
            print("✅ API credentials saved to config file")
        else:
            print("⚠️ You'll need to manually edit trading_config.ini before running the bot")
            return False
    else:
        print("✅ API credentials configured")
    
    return True

def test_binance_connection():
    """Test connection to Binance API"""
    print("🔗 Testing Binance API connection...")
    
    try:
        from binance.client import Client
        import configparser
        
        config = configparser.ConfigParser()
        config.read('trading_config.ini')
        
        api_key = config.get('binance', 'api_key')
        api_secret = config.get('binance', 'api_secret')
        testnet = config.getboolean('binance', 'testnet', fallback=True)
        
        client = Client(api_key, api_secret, testnet=testnet)
        
        # Test connection
        account = client.get_account()
        
        if testnet:
            print("✅ Connected to Binance Testnet successfully")
        else:
            print("✅ Connected to Binance Live API successfully")
            print("⚠️ WARNING: Live trading mode detected!")
        
        # Show balance
        for balance in account['balances']:
            if balance['asset'] == 'USDT' and float(balance['free']) > 0:
                print(f"💰 USDT Balance: {balance['free']}")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to connect to Binance API: {e}")
        print("Please check your API credentials and network connection")
        return False

def run_quick_test():
    """Run a quick test of the bot"""
    print("🧪 Running quick bot test...")
    
    try:
        from crypto_trading_bot import CryptoTradingBot, load_config_from_file
        
        config = load_config_from_file()
        config.crypto_pairs = ["BTCUSDT"]  # Test with just one pair
        
        bot = CryptoTradingBot(config)
        
        # Test data fetching
        print("📊 Testing data fetching...")
        bot.update_market_data()
        
        if "BTCUSDT" in bot.market_data and not bot.market_data["BTCUSDT"].empty:
            print("✅ Data fetching successful")
        else:
            print("❌ Data fetching failed")
            return False
        
        # Test prediction
        print("🔮 Testing prediction...")
        prediction = bot.make_prediction("BTCUSDT")
        
        if prediction:
            print(f"✅ Prediction successful: {prediction.predicted_gain:.2f}% gain predicted")
        else:
            print("❌ Prediction failed")
            return False
        
        print("✅ Quick test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Crypto Trading Bot Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return
    
    # Install requirements
    if not install_requirements():
        return
    
    # Setup configuration
    if not setup_config():
        return
    
    # Check models and scalers
    if not check_models_and_scalers():
        return
    
    # Test Binance connection
    if not test_binance_connection():
        return
    
    # Run quick test
    if not run_quick_test():
        return
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Review trading_config.ini settings")
    print("2. Start with test mode: python crypto_trading_bot.py")
    print("3. Choose option 1 (Test Mode) to paper trade")
    print("4. Monitor logs and performance")
    print("5. Only switch to live trading after thorough testing")
    print("\n⚠️ Remember: Start with small amounts and test thoroughly!")

if __name__ == "__main__":
    main()

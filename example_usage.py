#!/usr/bin/env python3
"""
Example usage of the Crypto Trading Bot
Demonstrates different ways to configure and run the bot
"""

from crypto_trading_bot import CryptoTradingBot, TradingConfig
import time

def example_test_mode():
    """Example: Run bot in test mode with custom configuration"""
    print("🧪 Example: Test Mode Configuration")
    
    # Create custom configuration
    config = TradingConfig(
        # API Configuration (use testnet for safety)
        api_key="your_testnet_api_key",
        api_secret="your_testnet_api_secret",
        testnet=True,
        
        # Trading Parameters
        max_budget=30.0,           # $50 budget
        trade_amount=14.0,         # $20 per trade
        max_concurrent_trades=2,   # Max 2 trades
        
        # ML Parameters
        prediction_threshold=1.9,  # 2% minimum gain
        take_profit=1.2,          # 1.2% take profit
        stop_loss=1.5,            # 1.5% stop loss
        
        # Crypto pairs to monitor
        crypto_pairs=["BTCUSDT", "ETHUSDT", "ADAUSDT"],
        
        # Model paths
        models_path="/path/to/your/models",
        scalers_path="/path/to/your/scalers"
    )
    
    # Initialize bot
    bot = CryptoTradingBot(config)
    
    # Run test mode for 2 hours
    bot.run_test_mode(duration_hours=2)

def example_conservative_config():
    """Example: Conservative trading configuration"""
    print("🛡️ Example: Conservative Configuration")
    
    config = TradingConfig(
        # Conservative parameters
        max_budget=30.0,
        trade_amount=10.0,         # Smaller trades
        max_concurrent_trades=1,   # Only one trade at a time
        
        # Higher thresholds for safety
        prediction_threshold=2.5,  # Higher prediction threshold
        take_profit=1.0,          # Lower take profit (more conservative)
        stop_loss=1.0,            # Tighter stop loss
        
        # Monitor fewer pairs for focus
        crypto_pairs=["BTCUSDT", "ETHUSDT"]
    )
    
    return config

def example_aggressive_config():
    """Example: Aggressive trading configuration"""
    print("⚡ Example: Aggressive Configuration")
    
    config = TradingConfig(
        # Aggressive parameters
        max_budget=100.0,
        trade_amount=25.0,         # Larger trades
        max_concurrent_trades=3,   # More concurrent trades
        
        # Lower thresholds for more trades
        prediction_threshold=1.5,  # Lower prediction threshold
        take_profit=2.0,          # Higher take profit target
        stop_loss=2.0,            # Wider stop loss
        
        # Monitor more pairs
        crypto_pairs=["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT", "DOTUSDT"]
    )
    
    return config

def example_monitoring_only():
    """Example: Monitor predictions without trading"""
    print("👁️ Example: Monitoring Mode")
    
    config = TradingConfig(
        # Set very high threshold to prevent trading
        prediction_threshold=10.0,  # 10% - unlikely to trigger
        
        # Monitor many pairs
        crypto_pairs=["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT"]
    )
    
    bot = CryptoTradingBot(config)
    
    # Run for 1 hour just to see predictions
    print("📊 Monitoring predictions for 1 hour...")
    start_time = time.time()
    
    try:
        while time.time() - start_time < 3600:  # 1 hour
            bot.update_market_data()
            
            # Get predictions for all pairs
            for symbol in config.crypto_pairs:
                prediction = bot.make_prediction(symbol)
                if prediction:
                    print(f"🔮 {symbol}: {prediction.predicted_gain:.2f}% predicted gain")
            
            time.sleep(300)  # Wait 5 minutes
            
    except KeyboardInterrupt:
        print("Monitoring stopped by user")

def example_custom_pairs():
    """Example: Trading specific crypto pairs"""
    print("🎯 Example: Custom Crypto Pairs")
    
    # Focus on specific pairs you're interested in
    custom_pairs = ["BTCUSDT", "ETHUSDT"]  # Only BTC and ETH
    
    config = TradingConfig(
        crypto_pairs=custom_pairs,
        max_concurrent_trades=len(custom_pairs),  # One trade per pair max
        prediction_threshold=1.8,
        
        # Adjust trade amount based on number of pairs
        trade_amount=15.0  # $15 per trade for 2 pairs
    )
    
    return config

def run_examples():
    """Run example configurations"""
    print("🚀 Crypto Trading Bot - Example Configurations")
    print("=" * 60)
    
    examples = {
        "1": ("Conservative Trading", example_conservative_config),
        "2": ("Aggressive Trading", example_aggressive_config),
        "3": ("Custom Pairs", example_custom_pairs),
        "4": ("Monitoring Only", example_monitoring_only),
        "5": ("Test Mode Demo", example_test_mode)
    }
    
    print("Available examples:")
    for key, (name, _) in examples.items():
        print(f"{key}. {name}")
    
    choice = input("\nSelect example (1-5): ").strip()
    
    if choice in examples:
        name, func = examples[choice]
        print(f"\n🎯 Running: {name}")
        
        if choice == "4":
            func()  # Monitoring only runs directly
        elif choice == "5":
            func()  # Test mode runs directly
        else:
            config = func()
            print("Configuration created. To use:")
            print("bot = CryptoTradingBot(config)")
            print("bot.run_test_mode(24)  # or bot.run_live_mode()")
    else:
        print("Invalid choice")

if __name__ == "__main__":
    # Show configuration examples
    run_examples()
    
    # Example of programmatic usage
    print("\n" + "=" * 60)
    print("📋 Programmatic Usage Example:")
    print("""
# 1. Create configuration
config = TradingConfig(
    api_key="your_api_key",
    api_secret="your_api_secret",
    testnet=True,
    max_budget=30.0,
    trade_amount=14.0,
    crypto_pairs=["BTCUSDT", "ETHUSDT"]
)

# 2. Initialize bot
bot = CryptoTradingBot(config)

# 3. Run in test mode
bot.run_test_mode(24)  # 24 hours

# 4. Or run live (be careful!)
# bot.run_live_mode()
    """)

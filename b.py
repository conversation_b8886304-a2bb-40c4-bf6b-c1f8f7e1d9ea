import glob
import pandas as pd
from sklearn.preprocessing import StandardScaler
import joblib

# 1. List all parquet files
parquet_files = glob.glob('*.parquet')

# 2. Load and combine
dfs = [pd.read_parquet(f) for f in parquet_files]
combined_df = pd.concat(dfs, ignore_index=True)

# 3. Extract features
feature_columns = [col for col in combined_df.columns 
                  if not col.startswith('Target_') 
                  and col not in ['Crypto_Pair', 'Timestamp']]
features = combined_df[feature_columns]

# 4. Fit and save scaler
scaler = StandardScaler()
scaler.fit(features)
joblib.dump(scaler, 'recreated_scaler.pkl')
print("✅ Scaler saved to recreated_scaler.pkl")

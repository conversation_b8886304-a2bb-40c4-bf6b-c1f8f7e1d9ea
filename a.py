#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Enhanced Trading Bot with:
- Full prediction logging
- Proper file handling
- Comprehensive trading metrics
"""

import os
import re
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import json
import traceback
import warnings
warnings.filterwarnings('ignore')

# Trading and API imports
from binance.client import Client
from binance.enums import *
import tensorflow as tf
import joblib

# Import your technical analyzer
from maximum_technical_analyzer import MaximumTechnicalAnalyzer

# Disable TensorFlow logging
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
tf.get_logger().setLevel('ERROR')

# ========================
# CONFIGURATION
# ========================
class TradingConfig:
    """Centralized trading configuration"""
    
    # Trading parameters
    TRADE_BUDGET = 14.0
    MAX_PARALLEL_TRADES = 2
    MIN_PREDICTED_GAIN = 1.9
    TAKE_PROFIT_PERCENT = 1.2
    STOP_LOSS_PERCENT = 1.5
    MAX_LOSS_PERCENT = 1.55
    TRANSACTION_FEE_PERCENT = 0.1
    TOTAL_FEE_PERCENT = TRANSACTION_FEE_PERCENT * 2
    PREDICTION_HORIZON = 10
    SEQUENCE_LENGTH = 60
    MODEL_PATH = 'multihorizon_checkpoint_19.keras'  # Update with your path
    SCALER_PATH = 'recreated_scaler.pkl'     # Update with your path
    API_KEY = os.getenv('BINANCE_API_KEY', 'GWf3NmhOQi9q2KIhDLcugL1smupKLfwSzlSmY3TwexJqNf4ehDpXs4qy2eeZYviW')
    API_SECRET = os.getenv('BINANCE_API_SECRET', 'gJCCFfWoUCzDFfs9b6JCH6Q6yMg3vKSqxK5k5EnQ4rwHJXT6jPBvnyVaaJky14pC')
    TESTNET = True
    DATA_FETCH_INTERVAL = 300
    LOOKBACK_HOURS = 72
    LOG_LEVEL = logging.INFO
    TRADE_LOG_FILE = 'trade_history.csv'
    PREDICTIONS_LOG_FILE = 'predictions_vs_reality.csv'
    PERFORMANCE_LOG_FILE = 'trading_performance.csv'
    MAIN_LOG_FILE = 'trading_bot.log'
    
    FEATURE_NAMES = [
            # ==== BASE OHLCV ====
            "Open", "High", "Low", "Close", "Volume",

            # ==== SIMPLE & EXPONENTIAL MOVING AVERAGES ====
            "SMA_2","EMA_2","SMA_3","EMA_3","SMA_4","EMA_4","SMA_5","EMA_5",
            "SMA_6","EMA_6","SMA_7","EMA_7","SMA_8","EMA_8","SMA_9","EMA_9",
            "SMA_10","EMA_10","SMA_11","EMA_11","SMA_12","EMA_12","SMA_13","EMA_13",
            "SMA_14","EMA_14","SMA_15","EMA_15","SMA_16","EMA_16","SMA_17","EMA_17",
            "SMA_18","EMA_18","SMA_19","EMA_19","SMA_20","EMA_20","SMA_21","EMA_21",
            "SMA_25","EMA_25","SMA_26","EMA_26","SMA_30","EMA_30","SMA_34","EMA_34",
            "SMA_40","EMA_40","SMA_50","EMA_50","SMA_55","EMA_55","SMA_60","EMA_60",
            "SMA_89","EMA_89","SMA_100","EMA_100","SMA_120","EMA_120",
            "SMA_144","EMA_144","SMA_200","EMA_200",

            # ==== ADVANCED MOVING AVERAGES ====
            "WMA_8","TEMA_8","DEMA_8","HMA_8",
            "WMA_10","TEMA_10","DEMA_10","HMA_10",
            "WMA_13","TEMA_13","DEMA_13","HMA_13",
            "WMA_20","TEMA_20","DEMA_20","HMA_20",
            "WMA_21","TEMA_21","DEMA_21","HMA_21",
            "WMA_34","TEMA_34","DEMA_34","HMA_34",
            "WMA_55","TEMA_55","DEMA_55","HMA_55",
            "WMA_89","TEMA_89","DEMA_89","HMA_89",
            "KAMA_10","KAMA_14","KAMA_20","KAMA_30",

            # ==== MOMENTUM OSCILLATORS ====
            # RSI family
            "RSI_2","RSI_VOLUME_2","RSI_HIGH_2","RSI_LOW_2",
            "RSI_6","RSI_VOLUME_6","RSI_HIGH_6","RSI_LOW_6",
            "RSI_7","RSI_VOLUME_7","RSI_HIGH_7","RSI_LOW_7",
            "RSI_9","RSI_VOLUME_9","RSI_HIGH_9","RSI_LOW_9",
            "RSI_11","RSI_VOLUME_11","RSI_HIGH_11","RSI_LOW_11",
            "RSI_14","RSI_VOLUME_14","RSI_HIGH_14","RSI_LOW_14",
            "RSI_17","RSI_VOLUME_17","RSI_HIGH_17","RSI_LOW_17",
            "RSI_21","RSI_VOLUME_21","RSI_HIGH_21","RSI_LOW_21",
            "RSI_25","RSI_VOLUME_25","RSI_HIGH_25","RSI_LOW_25",
            "RSI_30","RSI_VOLUME_30","RSI_HIGH_30","RSI_LOW_30",

            # Stochastic K / D / Stoch-RSI
            "STOCH_K_5_3","STOCH_D_5_3","STOCH_RSI_5",
            "STOCH_K_8_3","STOCH_D_8_3","STOCH_RSI_8",
            "STOCH_K_14_3","STOCH_D_14_3","STOCH_RSI_14",
            "STOCH_K_21_5","STOCH_D_21_5","STOCH_RSI_21",
            "STOCH_K_25_5","STOCH_D_25_5","STOCH_RSI_25",
            "STOCH_K_34_8","STOCH_D_34_8","STOCH_RSI_34",

            # Williams %R & CCI
            "WILLR_6","WILLR_10","WILLR_14","WILLR_21","WILLR_28","WILLR_34",
            "CCI_14","CCI_17","CCI_20","CCI_25","CCI_30","CCI_34","CCI_40",

            # Other oscillators
            "UO","CMO_14","CMO_20",
            "AWESOME_OSC","AC_OSC","MFI_BW",
            "WTO_10_21","WTO_14_28",

            # ==== TREND INDICATORS ====
            # ADX system
            "ADX_14","PLUS_DI_14","MINUS_DI_14","DX_14",
            "ADX_17","PLUS_DI_17","MINUS_DI_17","DX_17",
            "ADX_20","PLUS_DI_20","MINUS_DI_20","DX_20",
            "ADX_25","PLUS_DI_25","MINUS_DI_25","DX_25",
            "ADX_28","PLUS_DI_28","MINUS_DI_28","DX_28",

            # Aroon
            "AROON_UP_14","AROON_DOWN_14","AROON_OSC_14",
            "AROON_UP_20","AROON_DOWN_20","AROON_OSC_20",
            "AROON_UP_25","AROON_DOWN_25","AROON_OSC_25",
            "AROON_UP_28","AROON_DOWN_28","AROON_OSC_28",
            "AROON_UP_30","AROON_DOWN_30","AROON_OSC_30",

            # Ichimoku
            "ICHIMOKU_TENKAN","ICHIMOKU_KIJUN","ICHIMOKU_SENKOU_A","ICHIMOKU_SENKOU_B",
            "ICHIMOKU_CHIKOU","ICHIMOKU_CLOUD_GREEN","ICHIMOKU_ABOVE_CLOUD","ICHIMOKU_BELOW_CLOUD",

            # ==== MACD VARIANTS ====
            "MACD_5_13","MACD_SIGNAL_5_13","MACD_HIST_5_13",
            "MACD_8_21","MACD_SIGNAL_8_21","MACD_HIST_8_21",
            "MACD_12_26","MACD_SIGNAL_12_26","MACD_HIST_12_26",
            "MACD_19_39","MACD_SIGNAL_19_39","MACD_HIST_19_39",
            "MACD_6_12","MACD_SIGNAL_6_12","MACD_HIST_6_12",
            "MACD_24_52","MACD_SIGNAL_24_52","MACD_HIST_24_52",
            "MACD_7_14","MACD_SIGNAL_7_14","MACD_HIST_7_14",
            "MACD_15_30","MACD_SIGNAL_15_30","MACD_HIST_15_30",
            "DINAPOLI_MACD","DINAPOLI_SIGNAL","DINAPOLI_HIST",

            # ==== BOLLINGER BANDS & KELTNER CHANNELS ====
            # BB: period-stdDev keyed as period_stdDev*10
            "BB_UPPER_10_15","BB_LOWER_10_15","BB_WIDTH_10_15","BB_POSITION_10_15",
            "BB_UPPER_10_20","BB_LOWER_10_20","BB_WIDTH_10_20","BB_POSITION_10_20",
            "BB_UPPER_15_18","BB_LOWER_15_18","BB_WIDTH_15_18","BB_POSITION_15_18",
            "BB_UPPER_20_15","BB_LOWER_20_15","BB_WIDTH_20_15","BB_POSITION_20_15",
            "BB_UPPER_20_20","BB_LOWER_20_20","BB_WIDTH_20_20","BB_POSITION_20_20",
            "BB_UPPER_20_25","BB_LOWER_20_25","BB_WIDTH_20_25","BB_POSITION_20_25",
            "BB_UPPER_25_20","BB_LOWER_25_20","BB_WIDTH_25_20","BB_POSITION_25_20",
            "BB_UPPER_30_20","BB_LOWER_30_20","BB_WIDTH_30_20","BB_POSITION_30_20",
            "BB_UPPER_34_20","BB_LOWER_34_20","BB_WIDTH_34_20","BB_POSITION_34_20",
            "BB_UPPER_50_20","BB_LOWER_50_20","BB_WIDTH_50_20","BB_POSITION_50_20",

            # Keltner Channels
            "KC_UPPER_10","KC_MIDDLE_10","KC_LOWER_10","KC_WIDTH_10",
            "KC_UPPER_14","KC_MIDDLE_14","KC_LOWER_14","KC_WIDTH_14",
            "KC_UPPER_20","KC_MIDDLE_20","KC_LOWER_20","KC_WIDTH_20",
            "KC_UPPER_28","KC_MIDDLE_28","KC_LOWER_28","KC_WIDTH_28",

            # ==== VOLATILITY INDICATORS ====
            "ATR_7","ATR_PERCENT_7","ATR_10","ATR_PERCENT_10","ATR_14","ATR_PERCENT_14",
            "ATR_17","ATR_PERCENT_17","ATR_20","ATR_PERCENT_20","ATR_21","ATR_PERCENT_21",
            "ATR_28","ATR_PERCENT_28","ATR_30","ATR_PERCENT_30","ATR_34","ATR_PERCENT_34",
            "ATR_50","ATR_PERCENT_50",
            "VOLATILITY_5","PRICE_RANGE_5",
            "VOLATILITY_10","PRICE_RANGE_10",
            "VOLATILITY_14","PRICE_RANGE_14",
            "VOLATILITY_20","PRICE_RANGE_20",
            "VOLATILITY_30","PRICE_RANGE_30",
            "VOLATILITY_50","PRICE_RANGE_50",

            # ==== VOLUME INDICATORS ====
            "OBV","VWAP",
            "MFI_10","MFI_14","MFI_17","MFI_20","MFI_25","MFI_28",
            "CHAIKIN_OSC",
            "CHAIKIN_MF_10","CHAIKIN_MF_14","CHAIKIN_MF_20","CHAIKIN_MF_28",
            "VOLUME_SMA_5","VOLUME_RATIO_5","VOLUME_ROC_5",
            "VOLUME_SMA_10","VOLUME_RATIO_10","VOLUME_ROC_10",
            "VOLUME_SMA_14","VOLUME_RATIO_14","VOLUME_ROC_14",
            "VOLUME_SMA_15","VOLUME_RATIO_15","VOLUME_ROC_15",
            "VOLUME_SMA_20","VOLUME_RATIO_20","VOLUME_ROC_20",
            "VOLUME_SMA_25","VOLUME_RATIO_25","VOLUME_ROC_25",
            "VOLUME_SMA_30","VOLUME_RATIO_30","VOLUME_ROC_30",
            "VOLUME_SMA_50","VOLUME_RATIO_50","VOLUME_ROC_50",
            "VOLUME_OSC_5_10","VOLUME_OSC_10_20","VOLUME_OSC_14_28",
            "VOLUME_PRICE_TREND","EASE_OF_MOVEMENT","ACCUMULATION_DISTRIBUTION",

            # ==== PRICE ACTION & SUPPORT/RESISTANCE ====
            "TYPICAL_PRICE","MEDIAN_PRICE","WEIGHTED_CLOSE",
            "RANGE","BODY","UPPER_SHADOW","LOWER_SHADOW",
            "BODY_RANGE_RATIO","UPPER_SHADOW_RATIO","LOWER_SHADOW_RATIO",
            "HL_RATIO","CO_RATIO","HC_RATIO","LC_RATIO",
            "OC_RATIO","HO_RATIO","LO_RATIO",
            "HA_OPEN","HA_HIGH","HA_LOW","HA_CLOSE",
            "HA_BODY","HA_UPPER_SHADOW","HA_LOWER_SHADOW",
            "GAP_UP","GAP_DOWN","GAP_SIZE","GAP_PERCENTAGE",

            # Support/Resistance (window-grouped)
            "RESISTANCE_5","SUPPORT_5","RESISTANCE_DISTANCE_5","SUPPORT_DISTANCE_5","SUPPORT_RESISTANCE_RATIO_5",
            "RESISTANCE_8","SUPPORT_8","RESISTANCE_DISTANCE_8","SUPPORT_DISTANCE_8","SUPPORT_RESISTANCE_RATIO_8",
            "RESISTANCE_10","SUPPORT_10","RESISTANCE_DISTANCE_10","SUPPORT_DISTANCE_10","SUPPORT_RESISTANCE_RATIO_10",
            "RESISTANCE_13","SUPPORT_13","RESISTANCE_DISTANCE_13","SUPPORT_DISTANCE_13","SUPPORT_RESISTANCE_RATIO_13",
            "RESISTANCE_15","SUPPORT_15","RESISTANCE_DISTANCE_15","SUPPORT_DISTANCE_15","SUPPORT_RESISTANCE_RATIO_15",
            "RESISTANCE_20","SUPPORT_20","RESISTANCE_DISTANCE_20","SUPPORT_DISTANCE_20","SUPPORT_RESISTANCE_RATIO_20",
            "RESISTANCE_21","SUPPORT_21","RESISTANCE_DISTANCE_21","SUPPORT_DISTANCE_21","SUPPORT_RESISTANCE_RATIO_21",
            "RESISTANCE_25","SUPPORT_25","RESISTANCE_DISTANCE_25","SUPPORT_DISTANCE_25","SUPPORT_RESISTANCE_RATIO_25",
            "RESISTANCE_30","SUPPORT_30","RESISTANCE_DISTANCE_30","SUPPORT_DISTANCE_30","SUPPORT_RESISTANCE_RATIO_30",
            "RESISTANCE_34","SUPPORT_34","RESISTANCE_DISTANCE_34","SUPPORT_DISTANCE_34","SUPPORT_RESISTANCE_RATIO_34",
            "RESISTANCE_50","SUPPORT_50","RESISTANCE_DISTANCE_50","SUPPORT_DISTANCE_50","SUPPORT_RESISTANCE_RATIO_50",
            "RESISTANCE_55","SUPPORT_55","RESISTANCE_DISTANCE_55","SUPPORT_DISTANCE_55","SUPPORT_RESISTANCE_RATIO_55",

            # ==== MOMENTUM & TRIX ====
            "MOMENTUM_1","ROC_1","MOMENTUM_2","ROC_2","MOMENTUM_3","ROC_3","MOMENTUM_4","ROC_4",
            "MOMENTUM_5","ROC_5","MOMENTUM_6","ROC_6","MOMENTUM_7","ROC_7","MOMENTUM_8","ROC_8",
            "MOMENTUM_9","ROC_9","MOMENTUM_10","ROC_10","MOMENTUM_11","ROC_11","MOMENTUM_12","ROC_12",
            "MOMENTUM_14","ROC_14","MOMENTUM_16","ROC_16","MOMENTUM_18","ROC_18",
            "MOMENTUM_20","ROC_20","MOMENTUM_21","ROC_21","MOMENTUM_25","ROC_25",
            "MOMENTUM_28","ROC_28","MOMENTUM_30","ROC_30",
            "TRIX_5","TRIX_SIGNAL_5","TRIX_8","TRIX_SIGNAL_8","TRIX_10","TRIX_SIGNAL_10",
            "TRIX_14","TRIX_SIGNAL_14","TRIX_20","TRIX_SIGNAL_20","TRIX_28","TRIX_SIGNAL_28",

            # ==== HARMONIC & ELLIOTT ====
            "GARTLEY_XA","GARTLEY_AB","GARTLEY_BC",
            "BUTTERFLY_XA","BUTTERFLY_AB",
            "BAT_XA","BAT_AB",
            "CRAB_XA","CRAB_AB",
            "WAVE_RATIO_618","WAVE_RATIO_382","WAVE_RATIO_236","WAVE_RATIO_1618",
            "IMPULSE_WAVE_1","IMPULSE_WAVE_3","IMPULSE_WAVE_5",
            "CORRECTIVE_A","CORRECTIVE_B","CORRECTIVE_C",

            # ==== ADVANCED CANDLE PATTERNS ====
            "ABANDONED_BABY","BELT_HOLD_BULL","BELT_HOLD_BEAR","BREAKAWAY",
            "CONCEALING_BABY_SWALLOW","COUNTERATTACK","DRAGONFLY_DOJI","GRAVESTONE_DOJI",
            "HANGING_MAN","INVERTED_HAMMER","LONG_LEGGED_DOJI","MARUBOZU_WHITE","MARUBOZU_BLACK",
            "MATCHING_LOW","ON_NECK","PIERCING_LINE","RICKSHAW_MAN","RISING_THREE_METHODS",
            "SEPARATING_LINES","SHOOTING_STAR","SPINNING_TOP","STALLED_PATTERN",
            "STICK_SANDWICH","TAKURI","TASUKI_GAP","THREE_INSIDE_UP","THREE_OUTSIDE_UP",
            "THRUSTING","TRISTAR","UNIQUE_THREE_RIVER_BOTTOM","UPSIDE_GAP_TWO_CROWS",
            "DOJI","HAMMER","BULLISH_ENGULFING","BEARISH_ENGULFING",

            # ==== FIBONACCI & CORRELATION ====
            "FIB_23_6_8","FIB_38_2_8","FIB_50_0_8","FIB_61_8_8","FIB_78_6_8","FIB_RETRACEMENT_8",
            "FIB_23_6_13","FIB_38_2_13","FIB_50_0_13","FIB_61_8_13","FIB_78_6_13","FIB_RETRACEMENT_13",
            "FIB_23_6_21","FIB_38_2_21","FIB_50_0_21","FIB_61_8_21","FIB_78_6_21","FIB_RETRACEMENT_21",
            "FIB_23_6_34","FIB_38_2_34","FIB_50_0_34","FIB_61_8_34","FIB_78_6_34","FIB_RETRACEMENT_34",
            "FIB_23_6_55","FIB_38_2_55","FIB_50_0_55","FIB_61_8_55","FIB_78_6_55","FIB_RETRACEMENT_55",
            "PRICE_VOLUME_CORR_5","HIGH_LOW_CORR_5","OPEN_CLOSE_CORR_5",
            "PRICE_VOLUME_CORR_10","HIGH_LOW_CORR_10","OPEN_CLOSE_CORR_10",
            "PRICE_VOLUME_CORR_14","HIGH_LOW_CORR_14","OPEN_CLOSE_CORR_14",
            "PRICE_VOLUME_CORR_20","HIGH_LOW_CORR_20","OPEN_CLOSE_CORR_20",
            "PRICE_VOLUME_CORR_30","HIGH_LOW_CORR_30","OPEN_CLOSE_CORR_30",
            "RANGE_VOLUME_CORR_10","RANGE_VOLUME_CORR_20","BODY_VOLUME_CORR_10"
        ]

# ========================
# UTILITIES
# ========================
def remove_emojis(text):
    """Remove emojis from text for Windows compatibility"""
    emoji_pattern = re.compile("["
        u"\U0001F600-\U0001F64F"  # emoticons
        u"\U0001F300-\U0001F5FF"  # symbols & pictographs
        u"\U0001F680-\U0001F6FF"  # transport & map symbols
        u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
        u"\U00002702-\U000027B0"
        u"\U000024C2-\U0001F251"
        "]+", flags=re.UNICODE)
    return emoji_pattern.sub('', text)

class WindowsSafeLogger:
    """Logger that removes emojis on Windows systems"""
    def __init__(self, name):
        self.logger = logging.getLogger(name)
        self.is_windows = os.name == 'nt'
    
    def info(self, msg, *args, **kwargs):
        if self.is_windows:
            msg = remove_emojis(msg)
        self.logger.info(msg, *args, **kwargs)
    
    def warning(self, msg, *args, **kwargs):
        if self.is_windows:
            msg = remove_emojis(msg)
        self.logger.warning(msg, *args, **kwargs)
    
    def error(self, msg, *args, **kwargs):
        if self.is_windows:
            msg = remove_emojis(msg)
        self.logger.error(msg, *args, **kwargs)
    
    def debug(self, msg, *args, **kwargs):
        if self.is_windows:
            msg = remove_emojis(msg)
        self.logger.debug(msg, *args, **kwargs)

class TradingLogger(WindowsSafeLogger):
    """Enhanced logger for trading operations"""
    def __init__(self, config):
        super().__init__("TradingBot")
        self.config = config
        self._setup_logging()
        self.init_files()
    
    def _setup_logging(self):
        logger = logging.getLogger("TradingBot")
        logger.setLevel(self.config.LOG_LEVEL)
        
        # File handler with UTF-8 encoding
        file_handler = logging.FileHandler(self.config.MAIN_LOG_FILE, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    
    def init_files(self):
        """Initialize all logging files with headers"""
        # Trade history file
        if not os.path.exists(self.config.TRADE_LOG_FILE):
            trade_headers = [
                'timestamp', 'symbol', 'action', 'price', 'quantity', 
                'predicted_gain', 'actual_gain', 'fees_paid', 'net_profit',
                'roi_percent', 'duration_minutes', 'budget_after'
            ]
            pd.DataFrame(columns=trade_headers).to_csv(self.config.TRADE_LOG_FILE, index=False)
        
        # Predictions file
        if not os.path.exists(self.config.PREDICTIONS_LOG_FILE):
            pred_headers = [
                'timestamp', 'symbol', 'predicted_return_10steps', 
                'fee_adjusted_prediction', 'current_price', 'traded',
                'actual_return_1step', 'actual_return_5steps', 'actual_return_10steps',
                'prediction_accuracy_1step', 'prediction_accuracy_10steps'
            ]
            pd.DataFrame(columns=pred_headers).to_csv(self.config.PREDICTIONS_LOG_FILE, index=False)
        
        # Performance file
        if not os.path.exists(self.config.PERFORMANCE_LOG_FILE):
            perf_headers = [
                'timestamp', 'total_trades', 'successful_trades', 'success_rate',
                'total_profit', 'total_fees', 'net_profit', 'roi_percent',
                'sharpe_ratio', 'max_drawdown', 'current_budget'
            ]
            pd.DataFrame(columns=perf_headers).to_csv(self.config.PERFORMANCE_LOG_FILE, index=False)
    
    def log_trade(self, trade_data):
        """Log trade to CSV file"""
        try:
            pd.DataFrame([trade_data]).to_csv(
                self.config.TRADE_LOG_FILE, mode='a', header=False, index=False
            )
        except Exception as e:
            self.error(f"Trade logging failed: {str(e)}")
    
    def log_prediction(self, prediction_data):
        """Log prediction to CSV file"""
        try:
            pd.DataFrame([prediction_data]).to_csv(
                self.config.PREDICTIONS_LOG_FILE, mode='a', header=False, index=False
            )
        except Exception as e:
            self.error(f"Prediction logging failed: {str(e)}")
    
    def log_performance(self, performance_data):
        """Log performance metrics to CSV"""
        try:
            pd.DataFrame([performance_data]).to_csv(
                self.config.PERFORMANCE_LOG_FILE, mode='a', header=False, index=False
            )
        except Exception as e:
            self.error(f"Performance logging failed: {str(e)}")

# ========================
# CORE COMPONENTS
# ========================
class MarketDataManager:
    """Handles data fetching and feature preparation"""
    def __init__(self, client, analyzer, config, logger):
        self.client = client
        self.analyzer = analyzer
        self.config = config
        self.logger = logger
        self.last_fetch_times = {}
    
    def fetch_historical_data(self, symbol):
        """Fetch OHLCV data from Binance"""
        try:
            now = datetime.now(timezone.utc)
            
            # Check cache
            if symbol in self.last_fetch_times:
                if now - self.last_fetch_times[symbol] < timedelta(minutes=5):
                    return None
            
            # Fetch data
            start_time = int((now - timedelta(hours=self.config.LOOKBACK_HOURS)).timestamp() * 1000)
            klines = self.client.get_historical_klines(
                symbol=symbol,
                interval=Client.KLINE_INTERVAL_5MINUTE,
                start_str=start_time
            )
           
            #self.logger.error(f"Insufficient data for {symbol}: {len(klines)} bars, {klines}")

            if not klines:
                return None
                
            # Create DataFrame
            columns = ['open_time', 'Open', 'High', 'Low', 'Close', 'Volume',
                      'close_time', 'quote_asset_volume', 'num_trades',
                      'taker_buy_base', 'taker_buy_quote', 'ignore']
            df = pd.DataFrame(klines, columns=columns)
            
            # Convert to numeric
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Clean and return
            df = df[['Open', 'High', 'Low', 'Close', 'Volume']]
            self.last_fetch_times[symbol] = now
            return df.dropna()
            
        except Exception as e:
            self.logger.error(f"Data fetch error for {symbol}: {str(e)}")
            return None

    def calculate_features(self, df):
        """Calculate technical indicators with feature alignment"""
        try:
            # Calculate indicators
            feature_df = self.analyzer.add_maximum_technical_indicators(df)
            
            # Align with training features
            valid_features = [col for col in self.config.FEATURE_NAMES if col in feature_df.columns]
            missing_features = [col for col in self.config.FEATURE_NAMES if col not in feature_df.columns]
            
            # Add missing features with default value
            for col in missing_features:
                feature_df[col] = 0.0
                
            # Select only the required features
            return feature_df[valid_features + missing_features]
            
        except Exception as e:
            self.logger.error(f"Feature calculation error: {str(e)}")
            return None

class PredictionEngine:
    """Handles model predictions with full logging"""
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.model = tf.keras.models.load_model(config.MODEL_PATH)
        self.scaler = joblib.load(config.SCALER_PATH)
        self.logger.info("Model and scaler loaded")
    
    def predict_returns(self, feature_df, symbol, current_price):
        """Generate predictions and log results"""
        try:
            # Select and order features
            aligned_features = feature_df[self.config.FEATURE_NAMES]
            
            # Get last sequence
            sequence = aligned_features.tail(self.config.SEQUENCE_LENGTH)
            if len(sequence) < self.config.SEQUENCE_LENGTH:
                return None
                
            # Scale features
            scaled = self.scaler.transform(sequence)
            
            # Reshape for model
            input_data = scaled.reshape(1, self.config.SEQUENCE_LENGTH, len(self.config.FEATURE_NAMES))
            
            # Predict
            predictions = self.model.predict(input_data, verbose=0)[0]
            cumulative_return = (np.prod(1 + predictions) - 1) * 100
            
            # Calculate fee-adjusted return
            fee_adjusted_return = cumulative_return - self.config.TOTAL_FEE_PERCENT
            
            # Log prediction
            prediction_data = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'symbol': symbol,
                'predicted_return_10steps': cumulative_return,
                'fee_adjusted_prediction': fee_adjusted_return,
                'current_price': current_price,
                'traded': False,  # Will be updated if traded
                'actual_return_1step': 0,
                'actual_return_5steps': 0,
                'actual_return_10steps': 0,
                'prediction_accuracy_1step': 0,
                'prediction_accuracy_10steps': 0
            }
            self.logger.log_prediction(prediction_data)
            
            return cumulative_return, predictions, prediction_data
            
        except Exception as e:
            self.logger.error(f"Prediction error: {str(e)}")
            return None, None, None

class TradingEngine:
    """Manages trade execution and tracking"""
    def __init__(self, client, config, logger):
        self.client = client
        self.config = config
        self.logger = logger
        self.active_trades = {}
        self.budget = 100.0
        self.real_trading = False
        self.trade_count = 0
    
    def place_buy_order(self, symbol, predicted_gain, current_price, prediction_data):
        """Place buy order with OCO sell"""
        try:
            # Get current price
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            current_price = float(ticker['price'])
            
            # Calculate quantity
            quantity = self.config.TRADE_BUDGET / current_price
            
            if self.real_trading:
                # Place real order
                order = self.client.create_order(
                    symbol=symbol,
                    side=SIDE_BUY,
                    type=ORDER_TYPE_MARKET,
                    quantity=round(quantity, 6)
                )
                buy_price = float(order['fills'][0]['price'])
                fees = float(order['fills'][0]['commission'])
            else:
                # Simulated trade
                buy_price = current_price
                fees = buy_price * quantity * (self.config.TRANSACTION_FEE_PERCENT / 100)
                self.logger.info(f"SIM BUY: {quantity:.6f} {symbol} @ ${buy_price:.2f}")
            
            # Set OCO order
            take_profit = buy_price * (1 + self.config.TAKE_PROFIT_PERCENT / 100)
            stop_loss = buy_price * (1 - self.config.STOP_LOSS_PERCENT / 100)
            
            # Track trade
            self.active_trades[symbol] = {
                'entry_time': datetime.now(timezone.utc),
                'buy_price': buy_price,
                'quantity': quantity,
                'take_profit': take_profit,
                'stop_loss': stop_loss,
                'fees_paid': fees,
                'predicted_gain': predicted_gain,
                'prediction_data': prediction_data  # Store for later update
            }
            
            # Update prediction log to mark as traded
            prediction_data['traded'] = True
            self.logger.log_prediction(prediction_data)
            
            # Log trade entry
            trade_log = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'symbol': symbol,
                'action': 'BUY',
                'price': buy_price,
                'quantity': quantity,
                'predicted_gain': predicted_gain,
                'actual_gain': 0,
                'fees_paid': fees,
                'net_profit': 0,
                'roi_percent': 0,
                'duration_minutes': 0,
                'budget_after': self.budget
            }
            self.logger.log_trade(trade_log)
            
            # Update budget
            if not self.real_trading:
                self.budget -= (buy_price * quantity + fees)
                
            self.trade_count += 1
            return True
            
        except Exception as e:
            self.logger.error(f"Trade execution failed: {str(e)}")
            return False
    
    def check_active_trades(self):
        """Monitor and close completed trades"""
        completed = []
        for symbol, trade in self.active_trades.items():
            try:
                current_price = float(self.client.get_symbol_ticker(symbol=symbol)['price'])
                
                # Check if price hit targets
                if current_price >= trade['take_profit'] or current_price <= trade['stop_loss']:
                    completed.append(symbol)
                    
            except Exception as e:
                self.logger.error(f"Trade monitoring error: {str(e)}")
        
        # Close completed trades
        for symbol in completed:
            self.close_trade(symbol)
    
    def close_trade(self, symbol):
        """Close trade and calculate P&L with full logging"""
        trade = self.active_trades.pop(symbol)
        current_price = float(self.client.get_symbol_ticker(symbol=symbol)['price'])
        
        # Determine exit reason and price
        if current_price >= trade['take_profit']:
            exit_price = trade['take_profit']
            exit_type = "TP"
        elif current_price <= trade['stop_loss']:
            exit_price = trade['stop_loss']
            exit_type = "SL"
        else:
            exit_price = current_price
            exit_type = "MANUAL"
        
        # Calculate P&L
        gross_profit = (exit_price - trade['buy_price']) * trade['quantity']
        sell_fee = exit_price * trade['quantity'] * (self.config.TRANSACTION_FEE_PERCENT / 100)
        total_fees = trade['fees_paid'] + sell_fee
        net_profit = gross_profit - total_fees
        roi_percent = (net_profit / (trade['buy_price'] * trade['quantity'])) * 100
        
        # Calculate duration
        duration = (datetime.now(timezone.utc) - trade['entry_time']).total_seconds() / 60
        
        # Update budget in simulation
        if not self.real_trading:
            self.budget += (exit_price * trade['quantity'] - sell_fee)
        
        # Update prediction accuracy (for future implementation)
        # This would require tracking actual price movements
        
        # Log trade closure
        trade_log = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'symbol': symbol,
            'action': f'SELL_{exit_type}',
            'price': exit_price,
            'quantity': trade['quantity'],
            'predicted_gain': trade['predicted_gain'],
            'actual_gain': ((exit_price - trade['buy_price']) / trade['buy_price']) * 100,
            'fees_paid': total_fees,
            'net_profit': net_profit,
            'roi_percent': roi_percent,
            'duration_minutes': duration,
            'budget_after': self.budget
        }
        self.logger.log_trade(trade_log)
        
        self.logger.info(
            f"TRADE CLOSED: {symbol} {exit_type} | "
            f"Profit: ${net_profit:.2f} | ROI: {roi_percent:.2f}% | "
            f"Duration: {duration:.1f}min"
        )

# ========================
# MAIN TRADING BOT
# ========================
class TradingBot:
    def __init__(self, config):
        self.config = config
        self.logger = TradingLogger(config)
        self.client = Client(config.API_KEY, config.API_SECRET, testnet=config.TESTNET)
        self.analyzer = MaximumTechnicalAnalyzer(sequence_length=config.SEQUENCE_LENGTH)
        self.data_manager = MarketDataManager(self.client, self.analyzer, config, self.logger)
        self.prediction_engine = PredictionEngine(config, self.logger)
        self.trading_engine = TradingEngine(self.client, config, self.logger)
        self.symbols = self._get_valid_symbols()
        
        self.logger.info("Trading bot initialized successfully")
    
    def _get_valid_symbols(self):
        """Get valid trading symbols"""
        return ['BTCUSDC', 'ETHUSDC', 'ADAUSDC', 'XRPUSDC']
    
    def run_trading_cycle(self):
        """Execute one trading cycle with full logging"""
        try:
            # Check active trades
            self.trading_engine.check_active_trades()
            
            # Get predictions for all symbols
            predictions = []
            for symbol in self.symbols:
                try:
                    # Get current price for logging
                    ticker = self.client.get_symbol_ticker(symbol=symbol)
                    current_price = float(ticker['price'])
                    
                    # Fetch and process data
                    raw_data = self.data_manager.fetch_historical_data(symbol)
                    if raw_data is None or len(raw_data) < 100:
                        continue
                        
                    # Calculate features
                    features = self.data_manager.calculate_features(raw_data)
                    if features is None or features.empty:
                        continue
                        
                    # Get prediction (with automatic logging)
                    prediction_result = self.prediction_engine.predict_returns(
                        features, symbol, current_price
                    )
                    cumulative_return, _, prediction_data = prediction_result
                    
                    if cumulative_return is None:
                        continue
                    
                    self.logger.info(f"{symbol} prediction: {cumulative_return:.2f}%")
                    
                    predictions.append({
                        'symbol': symbol,
                        'predicted_gain': cumulative_return,
                        'prediction_data': prediction_data  # For trade reference
                    })
                    
                except Exception as e:
                    self.logger.error(f"Prediction failed for {symbol}: {str(e)}")
            
            # Execute trades
            if predictions:
                # Filter and sort predictions
                profitable = [p for p in predictions 
                            if p['predicted_gain'] >= self.config.MIN_PREDICTED_GAIN]
                profitable.sort(key=lambda x: x['predicted_gain'], reverse=True)
                
                # Execute top predictions
                for trade in profitable[:self.config.MAX_PARALLEL_TRADES]:
                    if len(self.trading_engine.active_trades) < self.config.MAX_PARALLEL_TRADES:
                        self.trading_engine.place_buy_order(
                            trade['symbol'], 
                            trade['predicted_gain'], 
                            current_price,
                            trade['prediction_data']
                        )
            
            # Log performance metrics
            self.log_performance_metrics()
            
        except Exception as e:
            self.logger.error(f"Trading cycle error: {str(e)}")
            traceback.print_exc()
    
    def log_performance_metrics(self):
        """Log performance metrics after each cycle"""
        performance = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'total_trades': self.trading_engine.trade_count,
            'successful_trades': 0,  # Would require tracking wins
            'success_rate': 0,
            'total_profit': 0,
            'total_fees': 0,
            'net_profit': 0,
            'roi_percent': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'current_budget': self.trading_engine.budget
        }
        self.logger.log_performance(performance)
    
    def run(self, real_trading=False):
        """Main trading loop"""
        self.trading_engine.real_trading = real_trading
        mode = "LIVE" if real_trading else "SIMULATION"
        self.logger.info(f"Starting {mode} mode")
        
        if real_trading:
            balance = self.client.get_asset_balance(asset='USDT')
            self.logger.info(f"Account balance: {balance['free']} USDT")
        else:
            self.logger.info(f"Simulation budget: ${self.trading_engine.budget}")
        
        cycle_count = 0
        try:
            while True:
                cycle_count += 1
                self.logger.info(f"Cycle #{cycle_count}")
                
                # Run trading cycle
                start_time = time.time()
                self.run_trading_cycle()
                
                # Calculate sleep time
                elapsed = time.time() - start_time
                sleep_time = max(1, self.config.DATA_FETCH_INTERVAL - elapsed)
                self.logger.info(f"Sleeping {sleep_time:.1f}s until next cycle")
                time.sleep(sleep_time)
                
        except KeyboardInterrupt:
            self.logger.info("Bot stopped by user")
        except Exception as e:
            self.logger.error(f"Critical error: {str(e)}")
            traceback.print_exc()

# ========================
# MAIN EXECUTION
# ========================
def main():
    config = TradingConfig()
    bot = TradingBot(config)
    bot.run(real_trading=False)  # Set to True for live trading

if __name__ == "__main__":
    # Add UTF-8 encoding support for Windows
    if os.name == 'nt':
        import sys
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
    
    main()

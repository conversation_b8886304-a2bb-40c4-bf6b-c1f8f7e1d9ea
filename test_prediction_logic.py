#!/usr/bin/env python3
"""
Test script to verify the new prediction logic that checks best price within prediction horizon
"""

import numpy as np
from datetime import datetime

# Mock prediction data to test the logic
def test_prediction_logic():
    """Test the new prediction logic with sample data"""
    
    # Sample prediction data
    current_price = 100.0
    prediction_threshold = 1.9  # 1.9% target gain
    
    # Test Case 1: Target reached at step 3, but final price is lower
    predicted_prices_1 = [100.5, 101.2, 102.1, 101.8, 101.5, 101.0, 100.8, 100.6, 100.4, 100.2]
    
    # Test Case 2: Target never reached
    predicted_prices_2 = [100.2, 100.4, 100.6, 100.8, 101.0, 101.2, 101.4, 101.6, 101.7, 101.8]
    
    # Test Case 3: Target reached at final step
    predicted_prices_3 = [100.1, 100.2, 100.3, 100.4, 100.5, 100.6, 100.7, 100.8, 101.0, 102.0]
    
    test_cases = [
        ("Target reached early, final lower", predicted_prices_1),
        ("Target never reached", predicted_prices_2),
        ("Target reached at end", predicted_prices_3)
    ]
    
    print("🧪 Testing New Prediction Logic")
    print("=" * 50)
    
    for case_name, predicted_prices in test_cases:
        print(f"\n📋 {case_name}")
        print(f"Current price: ${current_price:.2f}")
        print(f"Target threshold: {prediction_threshold}%")
        
        # Calculate gains (same logic as in trading bot)
        final_predicted_price = predicted_prices[-1]
        max_predicted_price = max(predicted_prices)
        min_predicted_price = min(predicted_prices)
        
        # Calculate gain percentages
        final_gain = ((final_predicted_price - current_price) / current_price) * 100
        max_gain = ((max_predicted_price - current_price) / current_price) * 100
        min_gain = ((min_predicted_price - current_price) / current_price) * 100
        
        # Find step where maximum gain occurs
        max_gain_step = predicted_prices.index(max_predicted_price) + 1
        
        # Check if target gain is reached at any point during prediction horizon
        target_reached = max_gain >= prediction_threshold
        
        print(f"📊 Results:")
        print(f"  Final gain: {final_gain:.2f}%")
        print(f"  Max gain: {max_gain:.2f}% (at step {max_gain_step})")
        print(f"  Min gain: {min_gain:.2f}%")
        print(f"  Target reached: {'✅ YES' if target_reached else '❌ NO'}")
        
        # Old logic vs New logic
        old_logic_trade = final_gain >= prediction_threshold
        new_logic_trade = target_reached
        
        print(f"🔄 Trading Decision:")
        print(f"  Old logic (final only): {'✅ BUY' if old_logic_trade else '❌ SKIP'}")
        print(f"  New logic (best price): {'✅ BUY' if new_logic_trade else '❌ SKIP'}")
        
        if old_logic_trade != new_logic_trade:
            print(f"  🎯 DIFFERENCE: New logic {'enables' if new_logic_trade else 'prevents'} this trade!")
        else:
            print(f"  ✅ Same decision with both logics")
    
    print("\n" + "=" * 50)
    print("🎉 Test completed!")
    print("\n💡 Key Benefits of New Logic:")
    print("  • More realistic: Can set take-profit orders")
    print("  • Better opportunities: Catches intraday peaks")
    print("  • Risk management: Shows worst-case scenarios")

if __name__ == "__main__":
    test_prediction_logic()

from binance.client import Client
import pandas as pd
import time
import datetime
from binance.helpers import interval_to_milliseconds
from tqdm import tqdm


# === CONFIG ===
API_KEY = 'GWf3NmhOQi9q2KIhDLcugL1smupKLfwSzlSmY3TwexJqNf4ehDpXs4qy2eeZYviW'
API_SECRET = 'gJCCFfWoUCzDFfs9b6JCH6Q6yMg3vKSqxK5k5EnQ4rwHJXT6jPBvnyVaaJky14pC'
BASE_SYMBOLS = [
    'XRP','SOL','TRX','DOGE','XLM','SUI','LINK','AVAX',# 'ADA',
    'HBAR','TON','UNI','SHIB','DOT','APT','WLD','NEAR','ICP',
    'ETC','ARB','FIL'
]
INTERVAL = Client.KLINE_INTERVAL_5MINUTE
START_STR = '1 Jan, 2017'
REQUEST_DELAY = 0.1  # seconds

client = Client(API_KEY, API_SECRET)
timeframe = interval_to_milliseconds(INTERVAL)

for base in BASE_SYMBOLS:
    symbol = base + 'USDT'
    print(f"\n📥 Starting download for {symbol}")

    # get starting timestamp
    start_ts = client._get_earliest_valid_timestamp(symbol, INTERVAL)
    if start_ts is None:
        print(f"⚠️ No data available for {symbol}, skipping.")
        continue

    now_ts = int(time.time() * 1000)
    total_bars = (now_ts - start_ts) // timeframe

    all_klines = []
    last_ts = start_ts

    with tqdm(total=total_bars, desc=symbol) as pbar:
        while True:
            klines = client.get_historical_klines(
                symbol, INTERVAL, start_str=last_ts, limit=1000
            )
            if not klines:
                break

            all_klines.extend(klines)
            pbar.update(len(klines))

            new_last = klines[-1][0] + timeframe
            if new_last <= last_ts:
                break
            last_ts = new_last

            time.sleep(REQUEST_DELAY)

    # build dataframe & save CSV
    df = pd.DataFrame(all_klines, columns=[
        'open_time','open','high','low','close','volume',
        'close_time','quote_asset_volume','num_trades',
        'taker_buy_base_volume','taker_buy_quote_volume','ignore'
    ])
    df['time'] = pd.to_datetime(df['open_time'], unit='ms')
    df = df[['time','open','high','low','close','volume']].astype(
        {'open':'float','high':'float','low':'float','close':'float','volume':'float'}
    )

    csv_fname = f"{symbol}_full.csv"
    df.to_csv(csv_fname, index=False)
    print(f"✅ Saved {len(df)} rows to {csv_fname}")